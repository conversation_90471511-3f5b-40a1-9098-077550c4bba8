from PyQt6.QtWidgets import <PERSON><PERSON><PERSON>ckedWidget, QWidget, QVBoxLayout, QLabel, QTabWidget
from PyQt6.QtCore import pyqtSignal
from styles import get_style

from ..stock.single_stock_single_agent import SingleStockSingleAgentPage
from ..stock.single_stock_multi_agent import SingleStockMultiAgentPage
from ..stock.multi_stock_analysis import MultiStockAnalysisPage
from ..settings.llm_config import LLMConfigView
from ..settings.theme_switcher import ThemeSwitcher


class PageManager(QStackedWidget):
    """页面管理器"""
    
    # 信号定义
    page_changed = pyqtSignal(int)  # 页面索引变化
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self._init_pages()
        
    def _init_pages(self):
        """初始化所有页面"""
        # 个股分析页面
        self.page_single_stock_single_agent = SingleStockSingleAgentPage()
        self.addWidget(self.page_single_stock_single_agent)
        
        self.page_single_stock_multi_agent = SingleStockMultiAgentPage()
        self.addWidget(self.page_single_stock_multi_agent)
        
        # 多股分析页面
        self.page_multi_stock_analysis = MultiStockAnalysisPage()
        self.addWidget(self.page_multi_stock_analysis)
        
        # 组合管理页面
        self._create_portfolio_build_page()
        self._create_risk_analysis_page()
        
        # 策略管理页面
        self._create_strategy_list_page()
        self._create_strategy_backtest_page()
        
        # 系统设置页面
        self._create_config_management_page()
        self._create_data_management_page()
        
        # 连接页面变化信号
        self.currentChanged.connect(self.page_changed.emit)
        
    def _create_portfolio_build_page(self):
        """创建组合构建页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.addWidget(QLabel("组合构建页面"))
        self.addWidget(page)
        
    def _create_risk_analysis_page(self):
        """创建风险分析页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.addWidget(QLabel("风险分析页面"))
        self.addWidget(page)
        
    def _create_strategy_list_page(self):
        """创建策略列表页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.addWidget(QLabel("策略列表页面"))
        self.addWidget(page)
        
    def _create_strategy_backtest_page(self):
        """创建策略回测页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.addWidget(QLabel("策略回测页面"))
        self.addWidget(page)
        
    def _create_config_management_page(self):
        """创建配置管理页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setSpacing(15)
        
        # 标题
        title = QLabel("配置管理")
        title.setStyleSheet(get_style("title"))
        layout.addWidget(title)
        
        # 创建标签页
        tab_widget = QTabWidget()
        tab_widget.setStyleSheet(get_style("tab"))
        
        # LLM配置标签页
        self.llm_config_view = LLMConfigView()
        tab_widget.addTab(self.llm_config_view, "LLM配置")
        
        # 主题设置标签页
        theme_tab = self._create_theme_settings_tab()
        tab_widget.addTab(theme_tab, "主题设置")
        
        # 其他配置标签页
        other_tab = self._create_other_settings_tab()
        tab_widget.addTab(other_tab, "其他设置")
        
        layout.addWidget(tab_widget)
        self.addWidget(page)
        
    def _create_theme_settings_tab(self):
        """创建主题设置标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setSpacing(15)
        
        # 主题设置标题
        theme_title = QLabel("界面主题")
        theme_title.setStyleSheet(get_style("title"))
        layout.addWidget(theme_title)
        
        # 主题说明
        theme_desc = QLabel("选择您喜欢的界面主题，系统将自动应用相应的颜色方案。")
        theme_desc.setStyleSheet(get_style("label"))
        theme_desc.setWordWrap(True)
        layout.addWidget(theme_desc)
        
        # 主题切换器
        self.theme_switcher = ThemeSwitcher()
        layout.addWidget(self.theme_switcher)
        
        # 主题预览区域
        preview_group = QWidget()
        preview_layout = QVBoxLayout(preview_group)
        preview_title = QLabel("主题预览")
        preview_title.setStyleSheet(get_style("title"))
        preview_layout.addWidget(preview_title)
        
        # 预览说明
        preview_desc = QLabel("当前主题的预览效果（实际效果可能略有不同）")
        preview_desc.setStyleSheet(get_style("label"))
        preview_layout.addWidget(preview_desc)
        
        layout.addWidget(preview_group)
        layout.addStretch()
        
        return tab
        
    def _create_other_settings_tab(self):
        """创建其他设置标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setSpacing(15)
        
        # 其他设置标题
        other_title = QLabel("其他设置")
        other_title.setStyleSheet(get_style("title"))
        layout.addWidget(other_title)
        
        # 其他设置说明
        other_desc = QLabel("其他系统设置选项")
        other_desc.setStyleSheet(get_style("label"))
        layout.addWidget(other_desc)
        
        layout.addStretch()
        
        return tab
        
    def _create_data_management_page(self):
        """创建数据管理页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.addWidget(QLabel("数据管理页面"))
        self.addWidget(page)
        
    def switch_to_page(self, page_name: str):
        """根据页面名称切换到对应页面"""
        page_mapping = {
            "单股单顾问分析": 0,
            "单股多顾问对比": 1,
            "多股分析": 2,
            "组合构建": 3,
            "风险分析": 4,
            "策略列表": 5,
            "策略回测": 6,
            "配置管理": 7,
            "数据管理": 8
        }
        
        if page_name in page_mapping:
            self.setCurrentIndex(page_mapping[page_name])
            
    def get_theme_switcher(self):
        """获取主题切换器"""
        return self.theme_switcher
        
    def get_llm_config_view(self):
        """获取LLM配置视图"""
        return self.llm_config_view
        
    def refresh_styles(self):
        """刷新所有页面样式"""
        for i in range(self.count()):
            widget = self.widget(i)
            if hasattr(widget, 'refresh_styles'):
                widget.refresh_styles()
                
        # 刷新配置页面中的组件
        if hasattr(self, 'theme_switcher'):
            self.theme_switcher.refresh_styles()
        if hasattr(self, 'llm_config_view'):
            self.llm_config_view.refresh_styles() 