"""
日志配置UI组件
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
    QCheckBox, QSpinBox, QGroupBox, QGridLayout, QTextEdit,
    QMessageBox, QFileDialog, QFrame
)
from PyQt6.QtCore import pyqtSignal, QTimer
from styles import get_style
from utils.config_manager import get_config_manager
from api.logger_config import logger_config
import os


class LoggingConfigView(QWidget):
    """日志配置页面"""

    config_changed = pyqtSignal(dict)  # 配置变化信号

    def __init__(self, parent=None):
        super().__init__(parent)
        
        # 初始化配置管理器
        self.config_manager = get_config_manager()
        
        self._init_ui()
        self._load_config()
        self._connect_signals()

    def _init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        layout.setSpacing(20)

        # 基础设置组
        self._create_basic_settings_group(layout)
        
        # 文件管理组
        self._create_file_management_group(layout)
        
        # 快速操作组
        self._create_quick_actions_group(layout)
        
        # 日志预览组
        self._create_log_preview_group(layout)
        
        # 操作按钮
        self._create_action_buttons(layout)

    def _create_basic_settings_group(self, parent_layout):
        """创建基础设置组"""
        group = QGroupBox("基础设置")
        group.setStyleSheet(get_style("group_box"))
        layout = QGridLayout(group)
        
        # 调试模式
        self.debug_enabled_cb = QCheckBox("启用调试模式")
        self.debug_enabled_cb.setStyleSheet(get_style("checkbox"))
        layout.addWidget(self.debug_enabled_cb, 0, 0, 1, 2)
        
        # 控制台输出
        self.console_enabled_cb = QCheckBox("控制台输出")
        self.console_enabled_cb.setStyleSheet(get_style("checkbox"))
        layout.addWidget(self.console_enabled_cb, 1, 0, 1, 2)
        
        # 文件输出
        self.file_enabled_cb = QCheckBox("文件输出")
        self.file_enabled_cb.setStyleSheet(get_style("checkbox"))
        layout.addWidget(self.file_enabled_cb, 2, 0, 1, 2)
        
        parent_layout.addWidget(group)

    def _create_file_management_group(self, parent_layout):
        """创建文件管理组"""
        group = QGroupBox("文件管理设置")
        group.setStyleSheet(get_style("group_box"))
        layout = QGridLayout(group)
        
        # 文件大小限制
        layout.addWidget(QLabel("单个文件大小限制 (MB):"), 0, 0)
        self.max_file_size_spin = QSpinBox()
        self.max_file_size_spin.setRange(1, 100)
        self.max_file_size_spin.setValue(10)
        self.max_file_size_spin.setStyleSheet(get_style("spinbox"))
        layout.addWidget(self.max_file_size_spin, 0, 1)
        
        # 最大文件数量
        layout.addWidget(QLabel("最大文件数量:"), 1, 0)
        self.max_files_spin = QSpinBox()
        self.max_files_spin.setRange(1, 20)
        self.max_files_spin.setValue(5)
        self.max_files_spin.setStyleSheet(get_style("spinbox"))
        layout.addWidget(self.max_files_spin, 1, 1)
        
        # 清理天数
        layout.addWidget(QLabel("自动清理天数:"), 2, 0)
        self.cleanup_days_spin = QSpinBox()
        self.cleanup_days_spin.setRange(1, 30)
        self.cleanup_days_spin.setValue(7)
        self.cleanup_days_spin.setStyleSheet(get_style("spinbox"))
        layout.addWidget(self.cleanup_days_spin, 2, 1)
        
        # 日志目录信息
        layout.addWidget(QLabel("日志目录:"), 3, 0)
        self.log_dir_label = QLabel()
        self.log_dir_label.setStyleSheet(get_style("label_info"))
        self.log_dir_label.setWordWrap(True)
        layout.addWidget(self.log_dir_label, 3, 1)
        
        # 打开日志目录按钮
        self.open_log_dir_btn = QPushButton("打开日志目录")
        self.open_log_dir_btn.setStyleSheet(get_style("button_secondary"))
        self.open_log_dir_btn.clicked.connect(self._open_log_directory)
        layout.addWidget(self.open_log_dir_btn, 4, 0, 1, 2)
        
        parent_layout.addWidget(group)

    def _create_quick_actions_group(self, parent_layout):
        """创建快速操作组"""
        group = QGroupBox("快速操作")
        group.setStyleSheet(get_style("group_box"))
        layout = QHBoxLayout(group)
        
        # 预设模式按钮
        self.full_logging_btn = QPushButton("完整日志")
        self.full_logging_btn.setStyleSheet(get_style("button_secondary"))
        self.full_logging_btn.clicked.connect(self._enable_full_logging)
        layout.addWidget(self.full_logging_btn)
        
        self.minimal_logging_btn = QPushButton("最小日志")
        self.minimal_logging_btn.setStyleSheet(get_style("button_secondary"))
        self.minimal_logging_btn.clicked.connect(self._enable_minimal_logging)
        layout.addWidget(self.minimal_logging_btn)
        
        self.disable_logging_btn = QPushButton("关闭日志")
        self.disable_logging_btn.setStyleSheet(get_style("button_secondary"))
        self.disable_logging_btn.clicked.connect(self._disable_logging)
        layout.addWidget(self.disable_logging_btn)
        
        # 分隔线
        separator = QFrame()
        separator.setFrameShape(QFrame.Shape.VLine)
        separator.setFrameShadow(QFrame.Shadow.Sunken)
        layout.addWidget(separator)
        
        # 管理操作
        self.clear_logs_btn = QPushButton("清空日志")
        self.clear_logs_btn.setStyleSheet(get_style("button_warning"))
        self.clear_logs_btn.clicked.connect(self._clear_logs)
        layout.addWidget(self.clear_logs_btn)
        
        self.reset_config_btn = QPushButton("重置配置")
        self.reset_config_btn.setStyleSheet(get_style("button_warning"))
        self.reset_config_btn.clicked.connect(self._reset_config)
        layout.addWidget(self.reset_config_btn)
        
        parent_layout.addWidget(group)

    def _create_log_preview_group(self, parent_layout):
        """创建日志预览组"""
        group = QGroupBox("日志文件信息")
        group.setStyleSheet(get_style("group_box"))
        layout = QVBoxLayout(group)
        
        # 日志文件列表
        self.log_info_text = QTextEdit()
        self.log_info_text.setMaximumHeight(120)
        self.log_info_text.setReadOnly(True)
        self.log_info_text.setStyleSheet(get_style("text_edit_readonly"))
        layout.addWidget(self.log_info_text)
        
        # 刷新按钮
        refresh_layout = QHBoxLayout()
        refresh_layout.addStretch()
        self.refresh_info_btn = QPushButton("刷新信息")
        self.refresh_info_btn.setStyleSheet(get_style("button_secondary"))
        self.refresh_info_btn.clicked.connect(self._refresh_log_info)
        refresh_layout.addWidget(self.refresh_info_btn)
        layout.addLayout(refresh_layout)
        
        parent_layout.addWidget(group)

    def _create_action_buttons(self, parent_layout):
        """创建操作按钮"""
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        self.save_btn = QPushButton("保存配置")
        self.save_btn.setStyleSheet(get_style("button_primary"))
        self.save_btn.clicked.connect(self._save_config)
        button_layout.addWidget(self.save_btn)
        
        self.apply_btn = QPushButton("应用")
        self.apply_btn.setStyleSheet(get_style("button_secondary"))
        self.apply_btn.clicked.connect(self._apply_config)
        button_layout.addWidget(self.apply_btn)
        
        parent_layout.addLayout(button_layout)

    def _connect_signals(self):
        """连接信号"""
        # 配置变化时启用应用按钮
        self.debug_enabled_cb.toggled.connect(self._on_config_changed)
        self.console_enabled_cb.toggled.connect(self._on_config_changed)
        self.file_enabled_cb.toggled.connect(self._on_config_changed)
        self.max_file_size_spin.valueChanged.connect(self._on_config_changed)
        self.max_files_spin.valueChanged.connect(self._on_config_changed)
        self.cleanup_days_spin.valueChanged.connect(self._on_config_changed)

    def _load_config(self):
        """加载配置"""
        config = self.config_manager.get_logging_config()
        
        # 设置UI控件值
        self.debug_enabled_cb.setChecked(config.get("debug_enabled", True))
        self.console_enabled_cb.setChecked(config.get("console_enabled", True))
        self.file_enabled_cb.setChecked(config.get("file_enabled", True))
        self.max_file_size_spin.setValue(config.get("max_file_size_mb", 10))
        self.max_files_spin.setValue(config.get("max_files", 5))
        self.cleanup_days_spin.setValue(config.get("cleanup_days", 7))
        
        # 更新日志目录显示
        self.log_dir_label.setText(logger_config.get_log_directory())
        
        # 刷新日志信息
        self._refresh_log_info()

    def _save_config(self):
        """保存配置"""
        config = self._get_current_config()
        
        # 保存到配置管理器
        if self.config_manager.set_logging_config(config):
            # 应用到日志系统
            self._apply_to_logger(config)
            
            QMessageBox.information(self, "成功", "日志配置已保存")
            self.config_changed.emit(config)
        else:
            QMessageBox.warning(self, "错误", "保存配置失败")

    def _apply_config(self):
        """应用配置（不保存）"""
        config = self._get_current_config()
        self._apply_to_logger(config)
        QMessageBox.information(self, "成功", "配置已应用")

    def _get_current_config(self) -> dict:
        """获取当前UI配置"""
        return {
            "debug_enabled": self.debug_enabled_cb.isChecked(),
            "console_enabled": self.console_enabled_cb.isChecked(),
            "file_enabled": self.file_enabled_cb.isChecked(),
            "max_file_size_mb": self.max_file_size_spin.value(),
            "max_files": self.max_files_spin.value(),
            "cleanup_days": self.cleanup_days_spin.value()
        }

    def _apply_to_logger(self, config: dict):
        """应用配置到日志系统"""
        logger_config.enable_debug(config.get("debug_enabled", True))
        logger_config.enable_console(config.get("console_enabled", True))
        logger_config.enable_file_logging(config.get("file_enabled", True))
        logger_config.set_file_size_limit(config.get("max_file_size_mb", 10))
        logger_config.set_max_files(config.get("max_files", 5))
        logger_config.set_cleanup_days(config.get("cleanup_days", 7))

    def _enable_full_logging(self):
        """启用完整日志"""
        self.debug_enabled_cb.setChecked(True)
        self.console_enabled_cb.setChecked(True)
        self.file_enabled_cb.setChecked(True)

    def _enable_minimal_logging(self):
        """启用最小日志"""
        self.debug_enabled_cb.setChecked(True)
        self.console_enabled_cb.setChecked(True)
        self.file_enabled_cb.setChecked(False)

    def _disable_logging(self):
        """关闭日志"""
        self.debug_enabled_cb.setChecked(False)
        self.console_enabled_cb.setChecked(False)
        self.file_enabled_cb.setChecked(False)

    def _clear_logs(self):
        """清空日志"""
        reply = QMessageBox.question(
            self, "确认", "确定要清空所有日志文件吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            logger_config.clear_all_logs()
            self._refresh_log_info()
            QMessageBox.information(self, "成功", "日志文件已清空")

    def _reset_config(self):
        """重置配置"""
        reply = QMessageBox.question(
            self, "确认", "确定要重置日志配置为默认值吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            # 重置UI
            self.debug_enabled_cb.setChecked(True)
            self.console_enabled_cb.setChecked(True)
            self.file_enabled_cb.setChecked(True)
            self.max_file_size_spin.setValue(10)
            self.max_files_spin.setValue(5)
            self.cleanup_days_spin.setValue(7)
            
            QMessageBox.information(self, "成功", "配置已重置为默认值")

    def _open_log_directory(self):
        """打开日志目录"""
        log_dir = logger_config.get_log_directory()
        if os.path.exists(log_dir):
            os.startfile(log_dir)  # Windows
        else:
            QMessageBox.warning(self, "错误", "日志目录不存在")

    def _refresh_log_info(self):
        """刷新日志信息"""
        try:
            log_files = logger_config.get_log_files()
            
            if not log_files:
                self.log_info_text.setText("暂无日志文件")
                return
            
            info_lines = []
            total_size = 0
            
            for log_file in log_files:
                size_mb = log_file['size_mb']
                total_size += size_mb
                info_lines.append(f"📄 {log_file['name']}: {size_mb:.2f} MB")
            
            info_lines.append(f"\n📊 总计: {len(log_files)} 个文件, {total_size:.2f} MB")
            
            self.log_info_text.setText("\n".join(info_lines))
            
        except Exception as e:
            self.log_info_text.setText(f"获取日志信息失败: {e}")

    def _on_config_changed(self):
        """配置变化处理"""
        # 可以在这里添加实时预览等功能
        pass
