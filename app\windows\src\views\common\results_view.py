import sys
import os
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTabWidget,
    QLabel, QTableWidget, QTableWidgetItem, QPushButton,
    QTextEdit, QScrollArea, QFrame, QSplitter, QProgressBar
)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal
from PyQt6.QtGui import QFont, QPixmap, QIcon
from typing import Dict, List, Any
import json

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from styles import get_style

# 尝试导入matplotlib，如果失败则使用占位符
try:
    import matplotlib.pyplot as plt
    from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
    from matplotlib.figure import Figure
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False
    # 创建占位符类
    class Figure:
        def __init__(self, figsize=None):
            pass
        def clear(self):
            pass
        def add_subplot(self, *args, **kwargs):
            return None
    
    class FigureCanvas(QWidget):
        def __init__(self, figure):
            super().__init__()
            self.figure = figure

class ResultsView(QWidget):
    """分析结果显示视图"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self._init_ui()
        
    def _init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(10)
        
        # 创建标题
        title = QLabel("分析结果")
        title.setStyleSheet(get_style("title"))
        layout.addWidget(title)
        
        # 创建标签页
        self.tab_widget = QTabWidget()
        
        # 创建各个标签页
        self._create_agents_tab()  # AI代理分析结果
        self._create_technical_tab()  # 技术分析
        self._create_fundamental_tab()  # 基本面分析
        self._create_sentiment_tab()  # 情绪分析
        self._create_valuation_tab()  # 估值分析
        self._create_portfolio_tab()  # 组合分析
        
        layout.addWidget(self.tab_widget)
        
        # 创建导出按钮
        self.export_btn = QPushButton("导出结果")
        self.export_btn.setStyleSheet(get_style("button_primary"))
        self.export_btn.clicked.connect(self._on_export)
        layout.addWidget(self.export_btn)
        
    def _create_agents_tab(self):
        """创建AI代理分析结果标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 创建滚动区域
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        scroll_content = QWidget()
        scroll_layout = QVBoxLayout(scroll_content)
        
        # 添加各个代理的分析结果
        agents = [
            "Aswath Damodaran", "Ben Graham", "Bill Ackman",
            "Cathie Wood", "Charlie Munger", "Michael Burry",
            "Peter Lynch", "Phil Fisher", "Rakesh Jhunjhunwala",
            "Stanley Druckenmiller", "Warren Buffett"
        ]
        
        for agent in agents:
            group = QWidget()
            group_layout = QVBoxLayout(group)
            
            # 代理名称
            title = QLabel(agent)
            title.setStyleSheet(get_style("title"))
            group_layout.addWidget(title)
            
            # 分析结果
            result = QTextEdit()
            result.setReadOnly(True)
            result.setMinimumHeight(100)
            group_layout.addWidget(result)
            
            scroll_layout.addWidget(group)
            
        scroll.setWidget(scroll_content)
        layout.addWidget(scroll)
        
        self.tab_widget.addTab(tab, "AI代理分析")
        
    def _create_technical_tab(self):
        """创建技术分析标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 创建图表
        figure = Figure(figsize=(8, 6))
        canvas = FigureCanvas(figure)
        layout.addWidget(canvas)
        
        # 创建技术指标表格
        table = QTableWidget()
        table.setColumnCount(4)
        table.setHorizontalHeaderLabels(["指标", "数值", "信号", "说明"])
        layout.addWidget(table)
        
        self.tab_widget.addTab(tab, "技术分析")
        
    def _create_fundamental_tab(self):
        """创建基本面分析标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 创建基本面数据表格
        table = QTableWidget()
        table.setColumnCount(3)
        table.setHorizontalHeaderLabels(["指标", "数值", "行业平均"])
        layout.addWidget(table)
        
        self.tab_widget.addTab(tab, "基本面分析")
        
    def _create_sentiment_tab(self):
        """创建情绪分析标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 创建情绪分析图表
        figure = Figure(figsize=(8, 6))
        canvas = FigureCanvas(figure)
        layout.addWidget(canvas)
        
        # 创建情绪指标表格
        table = QTableWidget()
        table.setColumnCount(3)
        table.setHorizontalHeaderLabels(["指标", "数值", "趋势"])
        layout.addWidget(table)
        
        self.tab_widget.addTab(tab, "情绪分析")
        
    def _create_valuation_tab(self):
        """创建估值分析标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 创建估值模型表格
        table = QTableWidget()
        table.setColumnCount(4)
        table.setHorizontalHeaderLabels(["模型", "估值", "当前价格", "溢价/折价"])
        layout.addWidget(table)
        
        self.tab_widget.addTab(tab, "估值分析")
        
    def _create_portfolio_tab(self):
        """创建组合分析标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 创建组合风险收益图表
        figure = Figure(figsize=(8, 6))
        canvas = FigureCanvas(figure)
        layout.addWidget(canvas)
        
        # 创建组合指标表格
        table = QTableWidget()
        table.setColumnCount(3)
        table.setHorizontalHeaderLabels(["指标", "数值", "基准"])
        layout.addWidget(table)
        
        self.tab_widget.addTab(tab, "组合分析")
        
    def _on_export(self):
        """导出结果处理"""
        # TODO: 实现导出功能
        pass
        
    def update_agent_analysis(self, agent_name, analysis):
        """更新AI代理分析结果"""
        # TODO: 实现更新逻辑
        
    def update_technical_analysis(self, data):
        """更新技术分析结果"""
        # TODO: 实现更新逻辑
        
    def update_fundamental_analysis(self, data):
        """更新基本面分析结果"""
        # TODO: 实现更新逻辑
        
    def update_sentiment_analysis(self, data):
        """更新情绪分析结果"""
        # TODO: 实现更新逻辑
        
    def update_valuation_analysis(self, data):
        """更新估值分析结果"""
        # TODO: 实现更新逻辑
        
    def update_portfolio_analysis(self, data):
        """更新组合分析结果"""
        # TODO: 实现更新逻辑
        
    def update_decisions(self, decisions):
        """更新交易决策"""
        # TODO: 实现交易决策更新逻辑
        pass
            
    def update_performance(self, performance):
        """更新收益分析"""
        # TODO: 实现收益分析更新逻辑
        pass
        
    def update_charts(self, data):
        """更新图表"""
        self.figure.clear()
        
        # 创建子图
        ax = self.figure.add_subplot(111)
        
        # 绘制数据
        for ticker, values in data.items():
            ax.plot(values['dates'], values['prices'], label=ticker)
            
        ax.set_title('股票价格走势')
        ax.set_xlabel('日期')
        ax.set_ylabel('价格')
        ax.legend()
        ax.grid(True)
        
        # 刷新画布
        self.canvas.draw()

    def update_results(self, results: Dict[str, Any]):
        """更新分析结果"""
        # 更新概览
        if "overview" in results:
            self.overview_text.setText(json.dumps(results["overview"], indent=2, ensure_ascii=False))
            
        # 更新技术分析
        if "technical" in results:
            self.technical_text.setText(json.dumps(results["technical"], indent=2, ensure_ascii=False))
            
        # 更新基本面分析
        if "fundamental" in results:
            self.fundamental_text.setText(json.dumps(results["fundamental"], indent=2, ensure_ascii=False))
            
        # 更新情绪分析
        if "sentiment" in results:
            self.sentiment_text.setText(json.dumps(results["sentiment"], indent=2, ensure_ascii=False))
            
        # 更新估值分析
        if "valuation" in results:
            self.valuation_text.setText(json.dumps(results["valuation"], indent=2, ensure_ascii=False))
            
        # 更新投资组合分析
        if "portfolio" in results:
            self.portfolio_text.setText(json.dumps(results["portfolio"], indent=2, ensure_ascii=False))
            
    def clear_results(self):
        """清空所有结果"""
        # 清空各个标签页的内容
        # TODO: 实现清空逻辑
        pass
        
    def refresh_styles(self):
        """刷新样式"""
        # 刷新标题样式
        for child in self.findChildren(QLabel):
            if child.text() == "分析结果":
                child.setStyleSheet(get_style("title"))
            else:
                child.setStyleSheet(get_style("label"))
        
        # 刷新按钮样式
        self.export_btn.setStyleSheet(get_style("button_primary"))
        
        # 刷新标签页样式
        self.tab_widget.setStyleSheet(get_style("tab")) 