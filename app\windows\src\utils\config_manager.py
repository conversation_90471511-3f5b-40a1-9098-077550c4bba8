"""
配置管理器
提供应用程序配置的持久化存储和管理功能
"""

import json
import os
import sys
from pathlib import Path
from typing import Dict, Any, Optional
import logging

class ConfigManager:
    """配置管理器类"""
    
    def __init__(self, app_name: str = "topai", config_filename: str = "config.json"):
        """
        初始化配置管理器
        
        Args:
            app_name: 应用程序名称，用于创建配置目录
            config_filename: 配置文件名
        """
        self.app_name = app_name
        self.config_filename = config_filename
        self.config_dir = self._get_config_directory()
        self.config_file_path = self.config_dir / self.config_filename
        
        # 确保配置目录存在
        self._ensure_config_directory()
        
        # 默认配置
        self.default_config = {
            "llm": {
                "selected_model": None,
                "api_base_url": "http://localhost:8000",
                "tab_index": 0
            },
            "app": {
                "theme": "default",
                "window_geometry": None,
                "last_used_stocks": [],
                "last_used_agents": []
            },
            "logging": {
                "debug_enabled": True,
                "console_enabled": True,
                "file_enabled": True,
                "max_file_size_mb": 10,
                "max_files": 5,
                "cleanup_days": 7
            }
        }
        
        # 当前配置
        self._config = self.default_config.copy()
        
        # 设置日志
        self.logger = logging.getLogger(__name__)
    
    def _get_config_directory(self) -> Path:
        """获取配置目录路径"""
        if sys.platform == "win32":
            # Windows: 使用 %APPDATA%
            base_dir = os.environ.get('APPDATA', os.path.expanduser('~'))
            config_dir = Path(base_dir) / self.app_name
        elif sys.platform == "darwin":
            # macOS: 使用 ~/Library/Application Support
            config_dir = Path.home() / "Library" / "Application Support" / self.app_name
        else:
            # Linux: 使用 ~/.config
            config_dir = Path.home() / ".config" / self.app_name
        
        # 如果无法写入用户目录，使用应用程序目录作为后备
        try:
            config_dir.mkdir(parents=True, exist_ok=True)
            # 测试写入权限
            test_file = config_dir / ".write_test"
            test_file.write_text("test")
            test_file.unlink()
        except (OSError, PermissionError):
            # 使用应用程序目录作为后备
            app_dir = Path(sys.executable).parent if getattr(sys, 'frozen', False) else Path(__file__).parent.parent
            config_dir = app_dir / "config"
            self.logger.warning(f"无法写入用户配置目录，使用应用程序目录: {config_dir}")
        
        return config_dir
    
    def _ensure_config_directory(self):
        """确保配置目录存在"""
        try:
            self.config_dir.mkdir(parents=True, exist_ok=True)
        except OSError as e:
            self.logger.error(f"无法创建配置目录 {self.config_dir}: {e}")
            raise
    
    def load_config(self) -> Dict[str, Any]:
        """
        加载配置文件
        
        Returns:
            Dict[str, Any]: 配置数据
        """
        try:
            if self.config_file_path.exists():
                with open(self.config_file_path, 'r', encoding='utf-8') as f:
                    loaded_config = json.load(f)
                
                # 合并默认配置和加载的配置
                self._config = self._merge_configs(self.default_config, loaded_config)
                self.logger.info(f"配置已从 {self.config_file_path} 加载")
            else:
                # 配置文件不存在，使用默认配置
                self._config = self.default_config.copy()
                self.logger.info("配置文件不存在，使用默认配置")
                
        except (json.JSONDecodeError, OSError) as e:
            self.logger.error(f"加载配置文件失败: {e}")
            # 备份损坏的配置文件
            self._backup_corrupted_config()
            # 使用默认配置
            self._config = self.default_config.copy()
        
        return self._config.copy()
    
    def save_config(self, config: Optional[Dict[str, Any]] = None) -> bool:
        """
        保存配置到文件
        
        Args:
            config: 要保存的配置数据，如果为None则保存当前配置
            
        Returns:
            bool: 保存是否成功
        """
        if config is not None:
            self._config = self._merge_configs(self._config, config)
        
        try:
            # 创建临时文件，确保原子性写入
            temp_file = self.config_file_path.with_suffix('.tmp')
            
            with open(temp_file, 'w', encoding='utf-8') as f:
                json.dump(self._config, f, indent=2, ensure_ascii=False)
            
            # 原子性替换
            temp_file.replace(self.config_file_path)
            
            self.logger.info(f"配置已保存到 {self.config_file_path}")
            return True
            
        except OSError as e:
            self.logger.error(f"保存配置文件失败: {e}")
            return False
    
    def get_llm_config(self) -> Dict[str, Any]:
        """获取LLM配置"""
        return self._config.get("llm", {}).copy()
    
    def set_llm_config(self, llm_config: Dict[str, Any]) -> bool:
        """
        设置LLM配置
        
        Args:
            llm_config: LLM配置数据
            
        Returns:
            bool: 设置是否成功
        """
        self._config["llm"] = self._merge_configs(self._config.get("llm", {}), llm_config)
        return self.save_config()
    
    def get_app_config(self) -> Dict[str, Any]:
        """获取应用程序配置"""
        return self._config.get("app", {}).copy()
    
    def set_app_config(self, app_config: Dict[str, Any]) -> bool:
        """
        设置应用程序配置
        
        Args:
            app_config: 应用程序配置数据
            
        Returns:
            bool: 设置是否成功
        """
        self._config["app"] = self._merge_configs(self._config.get("app", {}), app_config)
        return self.save_config()

    def get_logging_config(self) -> Dict[str, Any]:
        """获取日志配置"""
        return self._config.get("logging", {}).copy()

    def set_logging_config(self, logging_config: Dict[str, Any]) -> bool:
        """
        设置日志配置

        Args:
            logging_config: 日志配置数据

        Returns:
            bool: 设置是否成功
        """
        self._config["logging"] = self._merge_configs(self._config.get("logging", {}), logging_config)
        return self.save_config()

    def get_config(self) -> Dict[str, Any]:
        """
        获取完整配置

        Returns:
            Dict[str, Any]: 完整配置数据
        """
        return self._config.copy()

    def _merge_configs(self, base: Dict[str, Any], update: Dict[str, Any]) -> Dict[str, Any]:
        """
        递归合并配置字典
        
        Args:
            base: 基础配置
            update: 更新配置
            
        Returns:
            Dict[str, Any]: 合并后的配置
        """
        result = base.copy()
        
        for key, value in update.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._merge_configs(result[key], value)
            else:
                result[key] = value
        
        return result
    
    def _backup_corrupted_config(self):
        """备份损坏的配置文件"""
        if self.config_file_path.exists():
            try:
                backup_path = self.config_file_path.with_suffix('.backup')
                self.config_file_path.rename(backup_path)
                self.logger.info(f"损坏的配置文件已备份到: {backup_path}")
            except OSError as e:
                self.logger.error(f"备份损坏配置文件失败: {e}")
    
    def reset_to_defaults(self) -> bool:
        """
        重置配置为默认值
        
        Returns:
            bool: 重置是否成功
        """
        self._config = self.default_config.copy()
        return self.save_config()
    
    def get_config_file_path(self) -> str:
        """获取配置文件路径"""
        return str(self.config_file_path)


# 全局配置管理器实例
_config_manager = None

def get_config_manager() -> ConfigManager:
    """获取全局配置管理器实例"""
    global _config_manager
    if _config_manager is None:
        _config_manager = ConfigManager()
        _config_manager.load_config()
    return _config_manager
