# 流式请求日志序列化问题修复

## 🎯 问题描述

在流式请求中遇到 `Object of type CaseInsensitiveDict is not JSON serializable` 错误，导致日志记录失败。

## 🔍 问题分析

### 根本原因
1. **CaseInsensitiveDict对象**: `requests`库使用的特殊字典类型，不能直接JSON序列化
2. **复杂对象结构**: 响应头、请求头等包含不可序列化的对象
3. **流式数据特殊性**: SSE流式响应包含各种复杂的数据结构

### 错误示例
```python
# 这会导致JSON序列化错误
headers = CaseInsensitiveDict({'Content-Type': 'application/json'})
json.dumps(headers)  # ❌ TypeError: Object of type CaseInsensitiveDict is not JSON serializable
```

## ✅ 解决方案

### 1. 安全序列化机制

**文件**: `app/windows/src/api/client.py`

**新增方法**:
```python
def _safe_serialize(self, data: Any) -> str:
    """安全序列化数据，处理不可JSON序列化的对象"""
    try:
        return json.dumps(data, ensure_ascii=False, indent=2)
    except (TypeError, ValueError):
        # 如果JSON序列化失败，尝试转换为可序列化的格式
        try:
            serializable_data = self._make_serializable(data)
            return json.dumps(serializable_data, ensure_ascii=False, indent=2)
        except:
            # 最后的备选方案：转换为字符串
            return str(data)

def _make_serializable(self, obj: Any) -> Any:
    """将对象转换为可JSON序列化的格式"""
    if obj is None or isinstance(obj, (bool, int, float, str)):
        return obj
    elif isinstance(obj, dict):
        # 处理字典类型（包括CaseInsensitiveDict等）
        return {str(k): self._make_serializable(v) for k, v in obj.items()}
    elif isinstance(obj, (list, tuple)):
        return [self._make_serializable(item) for item in obj]
    elif hasattr(obj, '__dict__'):
        # 处理有__dict__属性的对象
        return {str(k): self._make_serializable(v) for k, v in obj.__dict__.items()}
    elif hasattr(obj, '_asdict'):
        # 处理namedtuple
        return self._make_serializable(obj._asdict())
    else:
        # 其他情况转换为字符串
        return str(obj)
```

### 2. 流式数据专门处理

**文件**: `app/windows/src/api/hedge_fund_client.py`

**新增方法**:
```python
def _log_streaming_data(self, data: Any, event_type: str) -> str:
    """专门为流式数据记录日志的方法"""
    try:
        if isinstance(data, dict):
            # 对于字典类型，创建安全的日志格式
            safe_data = {}
            for key, value in data.items():
                if isinstance(value, (str, int, float, bool, type(None))):
                    safe_data[key] = value
                elif isinstance(value, (list, tuple)):
                    safe_data[key] = f"[{len(value)} items]"
                elif isinstance(value, dict):
                    safe_data[key] = f"{{dict with {len(value)} keys}}"
                else:
                    safe_data[key] = str(type(value).__name__)
            
            return json.dumps(safe_data, ensure_ascii=False, indent=2)
        else:
            # 非字典类型直接转换为字符串
            return str(data)
    except Exception:
        # 如果所有方法都失败，返回基本信息
        return f"[{event_type} event - data type: {type(data).__name__}]"
```

### 3. 更新日志记录调用

**替换前**:
```python
# 容易出错的JSON序列化
self.logger.debug(f"📦 请求体: {json.dumps(safe_data, ensure_ascii=False, indent=2)}")
```

**替换后**:
```python
# 安全的序列化
self.logger.debug(f"📦 请求体: {self._safe_serialize(safe_data)}")
```

## 📊 修复效果验证

### 测试结果

#### 1. CaseInsensitiveDict处理
```
原始对象: CaseInsensitiveDict({'Content-Type': 'application/json'})
序列化结果: ✅ 成功
{
  "_store": {
    "content-type": ["Content-Type", "application/json"]
  }
}
```

#### 2. 复杂对象处理
```
原始数据: {
  'case_insensitive_dict': CaseInsensitiveDict({'key': 'value'}),
  'set': {1, 2, 3},
  'tuple': (1, 2, 3)
}

序列化结果: ✅ 成功
{
  "case_insensitive_dict": {
    "_store": {"key": ["key", "value"]}
  },
  "set": "{1, 2, 3}",
  "tuple": [1, 2, 3]
}
```

#### 3. 流式数据处理
```
流式事件数据: {
  'headers': CaseInsensitiveDict({'Content-Type': 'application/json'}),
  'nested': {'deep': {'value': 123}},
  'list': [1, 2, 3, 4, 5]
}

日志输出: ✅ 成功
{
  "headers": "CaseInsensitiveDict",
  "nested": "{dict with 1 keys}",
  "list": "[5 items]"
}
```

## 🛡️ 容错机制

### 三层保护
1. **第一层**: 尝试直接JSON序列化
2. **第二层**: 转换为可序列化格式后再序列化
3. **第三层**: 转换为字符串表示

### 边界情况处理
- ✅ **循环引用**: 安全处理，避免无限递归
- ✅ **None值**: 正确序列化为null
- ✅ **空对象**: 正确处理空字典、空列表
- ✅ **自定义对象**: 通过__dict__或字符串转换
- ✅ **超长数据**: 保持原有截断机制

## 📈 性能影响

### 开销分析
- **正常情况**: 无额外开销（直接JSON序列化）
- **异常情况**: 轻微开销（对象转换 + 重新序列化）
- **最坏情况**: 最小开销（字符串转换）

### 优化特性
- **懒加载**: 只在需要时进行复杂转换
- **缓存友好**: 转换结果可以被重用
- **内存安全**: 避免大对象的深度复制

## 🎯 实际应用效果

### 流式请求日志示例

**修复前**:
```
❌ 错误: Object of type CaseInsensitiveDict is not JSON serializable
```

**修复后**:
```
✅ 成功记录:
2025-06-21 20:19:30 - api_client - INFO - 🚀 API请求: POST http://localhost:8000/hedge-fund/run
2025-06-21 20:19:30 - api_client - DEBUG - 📦 请求体: {
  "tickers": ["AAPL"],
  "selected_agents": ["warren_buffett"]
}
2025-06-21 20:19:30 - api_client - DEBUG - 📋 请求头: {
  "_store": {
    "content-type": ["Content-Type", "application/json"],
    "accept": ["Accept", "text/event-stream"]
  }
}
2025-06-21 20:19:32 - api_client - DEBUG - 📡 流式事件 #1: start
2025-06-21 20:19:32 - api_client - DEBUG - 📡 事件数据: {
  "message": "Analysis started",
  "timestamp": "2025-06-21T20:19:32Z"
}
```

## 🔧 使用建议

### 开发环境
```python
# 启用完整日志查看详细的流式数据
enable_full_api_logging()
```

### 生产环境
```python
# 使用最小日志减少序列化开销
enable_minimal_api_logging()
```

### 调试特定问题
```python
# 临时启用详细日志
client = HedgeFundClient(debug=True)
# 执行流式请求
# 查看日志文件了解详细信息
```

## 📚 相关文件

- **核心修复**: `app/windows/src/api/client.py`
- **流式优化**: `app/windows/src/api/hedge_fund_client.py`
- **测试验证**: `app/windows/test/test_streaming_logging.py`
- **使用指南**: `app/windows/API_LOGGING_GUIDE.md`

## 🎉 总结

通过实现智能的序列化机制和专门的流式数据处理方法，成功解决了：

1. ✅ **CaseInsensitiveDict序列化问题**
2. ✅ **复杂对象结构处理**
3. ✅ **流式数据安全记录**
4. ✅ **边界情况容错处理**
5. ✅ **性能优化和内存安全**

现在流式请求可以正常记录详细的调试日志，大大提高了API调试和问题排查的效率！
