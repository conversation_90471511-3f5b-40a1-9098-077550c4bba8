import sys
import os
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QListWidget,
    QListWidgetItem, QPushButton, QComboBox, QCheckBox, QRadioButton,
    QButtonGroup, QScrollArea
)
from PyQt6.QtCore import Qt, pyqtSignal
from typing import List, Dict

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from styles import get_style

class AgentSelector(QWidget):
    """Agent选择组件"""
    
    agents_changed = pyqtSignal(list)  # 当Agent选择改变时发出信号
    
    def __init__(self, parent=None, multi_select=True):
        super().__init__(parent)
        self.multi_select = multi_select
        self.radio_buttons = {}  # 存储单选按钮的字典
        self.button_group = None  # 单选按钮组
        self._init_agents()
        self._init_ui()
        
    def _init_agents(self):
        """初始化Agent列表"""
        self.agents = {
            "Aswath Damodaran": {
                "name": "Aswath Damodaran",
                "description": "估值专家，专注于DCF模型和基本面分析",
                "specialty": "估值分析",
                "style": "价值投资"
            },
            "Ben Graham": {
                "name": "Ben Graham", 
                "description": "价值投资之父，安全边际理论创始人",
                "specialty": "价值投资",
                "style": "防御性投资"
            },
            "Bill Ackman": {
                "name": "Bill Ackman",
                "description": "激进投资者，专注于高质量成长股",
                "specialty": "成长投资",
                "style": "集中投资"
            },
            "Cathie Wood": {
                "name": "Cathie Wood",
                "description": "ARK投资创始人，专注于颠覆性创新",
                "specialty": "创新投资",
                "style": "成长投资"
            },
            "Charlie Munger": {
                "name": "Charlie Munger",
                "description": "伯克希尔副董事长，多学科思维模型",
                "specialty": "多学科分析",
                "style": "价值投资"
            },
            "Michael Burry": {
                "name": "Michael Burry",
                "description": "Scion资产管理创始人，逆向投资专家",
                "specialty": "逆向投资",
                "style": "价值投资"
            },
            "Peter Lynch": {
                "name": "Peter Lynch",
                "description": "富达麦哲伦基金经理，成长股投资大师",
                "specialty": "成长股分析",
                "style": "成长投资"
            },
            "Phil Fisher": {
                "name": "Phil Fisher",
                "description": "成长股投资之父，定性分析专家",
                "specialty": "定性分析",
                "style": "成长投资"
            },
            "Rakesh Jhunjhunwala": {
                "name": "Rakesh Jhunjhunwala",
                "description": "印度投资大师，长期价值投资者",
                "specialty": "长期投资",
                "style": "价值投资"
            },
            "Stanley Druckenmiller": {
                "name": "Stanley Druckenmiller",
                "description": "量子基金前经理，宏观对冲专家",
                "specialty": "宏观分析",
                "style": "对冲投资"
            },
            "Warren Buffett": {
                "name": "Warren Buffett",
                "description": "伯克希尔CEO，价值投资大师",
                "specialty": "价值投资",
                "style": "价值投资"
            }
        }
        
    def _init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        layout.setSpacing(10)

        # 标题
        title_text = "选择AI投资顾问" if self.multi_select else "选择一个AI投资顾问"
        title = QLabel(title_text)
        title.setStyleSheet(get_style("title"))
        layout.addWidget(title)

        if self.multi_select:
            # 多选模式：使用原有的快速选择和列表
            self._init_multi_select_ui(layout)
        else:
            # 单选模式：使用单选按钮
            self._init_single_select_ui(layout)

    def _init_multi_select_ui(self, layout):
        """初始化多选模式UI"""
        # 快速选择
        quick_layout = QHBoxLayout()
        quick_label = QLabel("快速选择:")
        quick_label.setStyleSheet(get_style("label"))
        self.quick_combo = QComboBox()
        self.quick_combo.setStyleSheet(get_style("combobox"))
        self.quick_combo.addItems([
            "全部选择",
            "价值投资专家",
            "成长投资专家",
            "技术分析专家",
            "基本面分析专家",
            "清空选择"
        ])
        self.quick_combo.currentTextChanged.connect(self._on_quick_select)
        quick_layout.addWidget(quick_label)
        quick_layout.addWidget(self.quick_combo)
        quick_layout.addStretch()
        layout.addLayout(quick_layout)

        # Agent列表
        self.agent_list = QListWidget()
        self.agent_list.setStyleSheet(get_style("list"))
        self.agent_list.setSelectionMode(QListWidget.SelectionMode.MultiSelection)
        self._populate_agent_list()
        self.agent_list.itemSelectionChanged.connect(self._on_selection_changed)
        layout.addWidget(self.agent_list)

        # 操作按钮
        button_layout = QHBoxLayout()

        self.select_all_btn = QPushButton("全选")
        self.select_all_btn.setStyleSheet(get_style("button_secondary"))
        self.select_all_btn.clicked.connect(self._select_all)
        button_layout.addWidget(self.select_all_btn)

        self.clear_btn = QPushButton("清空")
        self.clear_btn.setStyleSheet(get_style("button_secondary"))
        self.clear_btn.clicked.connect(self._clear_selection)
        button_layout.addWidget(self.clear_btn)

        button_layout.addStretch()
        layout.addLayout(button_layout)

    def _init_single_select_ui(self, layout):
        """初始化单选模式UI"""
        # 创建下拉框选择
        self.agent_combo = QComboBox()
        self.agent_combo.setStyleSheet(get_style("combobox"))

        # 填充下拉框选项
        first_agent = None
        for agent_name, agent_info in self.agents.items():
            display_text = f"{agent_name} - {agent_info['specialty']}"
            self.agent_combo.addItem(display_text, agent_name)

            # 记录第一个代理
            if first_agent is None:
                first_agent = agent_name

        # 设置第一个顾问为默认选择
        if self.agent_combo.count() > 0:
            self.agent_combo.setCurrentIndex(0)
            # 触发选择变化事件
            self._on_combo_selection_changed(0)

        # 连接信号
        self.agent_combo.currentIndexChanged.connect(self._on_combo_selection_changed)

        layout.addWidget(self.agent_combo)

        # 添加选中代理的详细信息显示
        self.agent_info_label = QLabel()
        self.agent_info_label.setStyleSheet(get_style("tips"))
        self.agent_info_label.setWordWrap(True)
        self.agent_info_label.setMinimumHeight(60)
        layout.addWidget(self.agent_info_label)

        # 显示第一个代理的信息
        if first_agent:
            self._update_agent_info_display(first_agent)
        
    def _populate_agent_list(self):
        """填充Agent列表（仅用于多选模式）"""
        if not hasattr(self, 'agent_list'):
            return
        self.agent_list.clear()
        for agent_name, agent_info in self.agents.items():
            item = QListWidgetItem()
            item.setText(f"{agent_name} - {agent_info['specialty']}")
            item.setData(Qt.ItemDataRole.UserRole, agent_name)
            item.setToolTip(agent_info['description'])
            self.agent_list.addItem(item)

    def _on_combo_selection_changed(self, index):
        """下拉框选择变化处理"""
        if not self.multi_select and index >= 0:
            # 获取选中的代理名称
            agent_name = self.agent_combo.itemData(index)
            if agent_name:
                # 更新代理信息显示
                self._update_agent_info_display(agent_name)
                # 发出信号
                self.agents_changed.emit([agent_name])

    def _update_agent_info_display(self, agent_name):
        """更新代理信息显示"""
        if agent_name in self.agents and hasattr(self, 'agent_info_label'):
            agent_info = self.agents[agent_name]
            info_text = f"📊 {agent_info['description']}\n💼 投资风格: {agent_info['style']}"
            self.agent_info_label.setText(info_text)
            
    def _on_quick_select(self, text: str):
        """快速选择处理"""
        if text == "全部选择":
            self._select_all()
        elif text == "价值投资专家":
            self._select_by_style("价值投资")
        elif text == "成长投资专家":
            self._select_by_style("成长投资")
        elif text == "技术分析专家":
            self._select_by_specialty("技术分析")
        elif text == "基本面分析专家":
            self._select_by_specialty("基本面分析")
        elif text == "清空选择":
            self._clear_selection()
            
    def _select_by_style(self, style: str):
        """按投资风格选择"""
        self.agent_list.clearSelection()
        for i in range(self.agent_list.count()):
            item = self.agent_list.item(i)
            agent_name = item.data(Qt.ItemDataRole.UserRole)
            if self.agents[agent_name]['style'] == style:
                item.setSelected(True)
        self._on_selection_changed()
        
    def _select_by_specialty(self, specialty: str):
        """按专业领域选择"""
        self.agent_list.clearSelection()
        for i in range(self.agent_list.count()):
            item = self.agent_list.item(i)
            agent_name = item.data(Qt.ItemDataRole.UserRole)
            if self.agents[agent_name]['specialty'] == specialty:
                item.setSelected(True)
        self._on_selection_changed()
        
    def _select_all(self):
        """全选"""
        for i in range(self.agent_list.count()):
            self.agent_list.item(i).setSelected(True)
        self._on_selection_changed()
        
    def _clear_selection(self):
        """清空选择"""
        self.agent_list.clearSelection()
        self._on_selection_changed()
        
    def _on_selection_changed(self):
        """选择变化处理（仅用于多选模式）"""
        if not self.multi_select:
            return
        selected_agents = []
        for item in self.agent_list.selectedItems():
            agent_name = item.data(Qt.ItemDataRole.UserRole)
            selected_agents.append(agent_name)
        self.agents_changed.emit(selected_agents)

    def get_selected_agents(self) -> List[str]:
        """获取选中的Agent列表"""
        selected_agents = []

        if self.multi_select and hasattr(self, 'agent_list'):
            # 多选模式：从列表获取
            for item in self.agent_list.selectedItems():
                agent_name = item.data(Qt.ItemDataRole.UserRole)
                selected_agents.append(agent_name)
        else:
            # 单选模式：从下拉框获取
            if hasattr(self, 'agent_combo') and self.agent_combo.currentIndex() >= 0:
                agent_name = self.agent_combo.itemData(self.agent_combo.currentIndex())
                if agent_name:
                    selected_agents.append(agent_name)

        return selected_agents

    def set_selected_agents(self, agents: List[str]):
        """设置选中的Agent列表"""
        if self.multi_select and hasattr(self, 'agent_list'):
            # 多选模式：设置列表选择
            self.agent_list.clearSelection()
            for i in range(self.agent_list.count()):
                item = self.agent_list.item(i)
                agent_name = item.data(Qt.ItemDataRole.UserRole)
                if agent_name in agents:
                    item.setSelected(True)
        else:
            # 单选模式：设置下拉框选择
            if hasattr(self, 'agent_combo') and agents:
                first_agent = agents[0]  # 单选模式只取第一个
                for i in range(self.agent_combo.count()):
                    if self.agent_combo.itemData(i) == first_agent:
                        self.agent_combo.setCurrentIndex(i)
                        self._update_agent_info_display(first_agent)
                        break
                
    def refresh_styles(self):
        """刷新样式"""
        title = self.findChild(QLabel)
        if title:
            title.setStyleSheet(get_style("title"))

        for child in self.findChildren(QLabel):
            if child != title and child != getattr(self, 'agent_info_label', None):
                child.setStyleSheet(get_style("label"))

        if self.multi_select:
            # 多选模式样式
            if hasattr(self, 'quick_combo'):
                self.quick_combo.setStyleSheet(get_style("combobox"))
            if hasattr(self, 'agent_list'):
                self.agent_list.setStyleSheet(get_style("list"))
            if hasattr(self, 'select_all_btn'):
                self.select_all_btn.setStyleSheet(get_style("button_secondary"))
            if hasattr(self, 'clear_btn'):
                self.clear_btn.setStyleSheet(get_style("button_secondary"))
        else:
            # 单选模式样式
            if hasattr(self, 'agent_combo'):
                self.agent_combo.setStyleSheet(get_style("combobox"))
            if hasattr(self, 'agent_info_label'):
                self.agent_info_label.setStyleSheet(get_style("tips"))