import sys
import os
from PyQt6.QtWidgets import (
    Q<PERSON><PERSON>t, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit,
    QPushButton, QComboBox, QSpinBox, QCheckBox, QGroupBox,
    QFormLayout, QTextEdit, QTabWidget, QListWidget, QListWidgetItem,
    QMessageBox, QProgressBar
)
from PyQt6.QtCore import Qt, pyqtSignal, QThread
from typing import Dict, Any, Optional

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from styles import get_style
from api import HedgeFundClient

class ModelFetchWorker(QThread):
    """获取模型列表的工作线程"""

    models_fetched = pyqtSignal(dict)  # 模型获取成功信号
    error_occurred = pyqtSignal(str)   # 错误信号

    def __init__(self, api_base_url: str = "http://localhost:8000"):
        super().__init__()
        self.api_base_url = api_base_url

    def run(self):
        """在后台线程中获取模型列表"""
        try:
            client = HedgeFundClient(self.api_base_url)
            models_data = client.get_language_models()
            self.models_fetched.emit(models_data)
        except Exception as e:
            self.error_occurred.emit(str(e))


class LLMConfigView(QWidget):
    """LLM配置页面"""

    config_changed = pyqtSignal(dict)  # 配置变化信号

    def __init__(self, parent=None, api_base_url: str = "http://localhost:8000"):
        super().__init__(parent)
        self.api_base_url = api_base_url
        self.selected_model = None
        self.available_models = []
        self._init_ui()
        self._load_default_config()
        self._load_available_models()

    def _init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        layout.setSpacing(15)

        # 标题
        # title = QLabel("LLM配置")
        # title.setStyleSheet(get_style("title"))
        # layout.addWidget(title)

        # 创建标签页（左侧标签）
        self.tab_widget = QTabWidget()
        self.tab_widget.setTabPosition(QTabWidget.TabPosition.West)
        self.tab_widget.setStyleSheet(get_style("tab"))

        # 可用模型标签页
        self._create_available_models_tab()

        # 自定义配置标签页
        self._create_custom_config_tab()

        layout.addWidget(self.tab_widget)

        # 操作按钮
        button_layout = QHBoxLayout()
        button_layout.addStretch()

        self.test_btn = QPushButton("测试连接")
        self.test_btn.setStyleSheet(get_style("button_secondary"))
        self.test_btn.clicked.connect(self._test_connection)
        button_layout.addWidget(self.test_btn)

        self.save_btn = QPushButton("保存配置")
        self.save_btn.setStyleSheet(get_style("button_primary"))
        self.save_btn.clicked.connect(self._save_config)
        button_layout.addWidget(self.save_btn)

        layout.addLayout(button_layout)
        
    def _create_available_models_tab(self):
        """创建可用模型标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # 模型选择组
        model_group = QGroupBox("可用模型")
        model_group.setStyleSheet(get_style("groupbox"))
        model_layout = QVBoxLayout(model_group)

        # 加载状态指示器
        self.loading_label = QLabel("正在加载模型列表...")
        self.loading_label.setStyleSheet(get_style("label"))
        self.loading_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        model_layout.addWidget(self.loading_label)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 0)  # 无限进度条
        self.progress_bar.setStyleSheet(get_style("progress_bar"))
        model_layout.addWidget(self.progress_bar)

        # 模型列表（初始隐藏）
        self.models_list = QComboBox()
        self.models_list.setStyleSheet(get_style("combobox"))
        self.models_list.setVisible(False)
        self.models_list.currentTextChanged.connect(self._on_model_selected)
        model_layout.addWidget(self.models_list)

        # 错误信息标签（初始隐藏）
        self.error_label = QLabel()
        self.error_label.setStyleSheet("color: #e74c3c; font-weight: bold;")
        self.error_label.setVisible(False)
        self.error_label.setWordWrap(True)
        model_layout.addWidget(self.error_label)

        # 重新加载按钮（初始隐藏）
        self.reload_btn = QPushButton("重新加载")
        self.reload_btn.setStyleSheet(get_style("button_secondary"))
        self.reload_btn.setVisible(False)
        self.reload_btn.clicked.connect(self._load_available_models)
        model_layout.addWidget(self.reload_btn)

        layout.addWidget(model_group)

        # 模型详情组
        details_group = QGroupBox("模型详情")
        details_group.setStyleSheet(get_style("groupbox"))
        details_layout = QVBoxLayout(details_group)

        self.model_details = QTextEdit()
        self.model_details.setStyleSheet(get_style("input"))
        self.model_details.setReadOnly(True)
        self.model_details.setMaximumHeight(80)  # 减小高度
        self.model_details.setMinimumHeight(80)
        self.model_details.setPlainText("请选择一个模型以查看详细信息")
        details_layout.addWidget(self.model_details)

        layout.addWidget(details_group)
        layout.addStretch()

        self.tab_widget.addTab(tab, "可用模型")
        
    def _create_custom_config_tab(self):
        """创建自定义配置标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # 开发中提示
        coming_soon_group = QGroupBox("自定义LLM配置")
        coming_soon_group.setStyleSheet(get_style("groupbox"))
        coming_soon_layout = QVBoxLayout(coming_soon_group)

        # 主要提示信息
        main_info = QLabel("🚧 功能开发中")
        main_info.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #f39c12;
                padding: 20px;
            }
        """)
        main_info.setAlignment(Qt.AlignmentFlag.AlignCenter)
        coming_soon_layout.addWidget(main_info)

        # 详细说明
        description = QLabel("""
        自定义LLM配置功能正在开发中，将包括以下特性：

        • 自定义API端点配置
        • 高级模型参数调整
        • 自定义提示词模板
        • 模型性能监控
        • 批量配置管理

        敬请期待后续版本更新！
        """)
        description.setStyleSheet(get_style("label"))
        description.setWordWrap(True)
        description.setAlignment(Qt.AlignmentFlag.AlignTop)
        coming_soon_layout.addWidget(description)

        layout.addWidget(coming_soon_group)

        # 占位符表单（禁用状态）
        placeholder_group = QGroupBox("配置预览（开发中）")
        placeholder_group.setStyleSheet(get_style("groupbox"))
        placeholder_layout = QFormLayout(placeholder_group)

        # 禁用的输入字段作为预览
        self.custom_api_url = QLineEdit()
        self.custom_api_url.setPlaceholderText("自定义API地址")
        self.custom_api_url.setStyleSheet(get_style("input"))
        self.custom_api_url.setEnabled(False)
        placeholder_layout.addRow("API地址:", self.custom_api_url)

        self.custom_api_key = QLineEdit()
        self.custom_api_key.setPlaceholderText("API密钥")
        self.custom_api_key.setEchoMode(QLineEdit.EchoMode.Password)
        self.custom_api_key.setStyleSheet(get_style("input"))
        self.custom_api_key.setEnabled(False)
        placeholder_layout.addRow("API密钥:", self.custom_api_key)

        self.custom_model_name = QLineEdit()
        self.custom_model_name.setPlaceholderText("模型名称")
        self.custom_model_name.setStyleSheet(get_style("input"))
        self.custom_model_name.setEnabled(False)
        placeholder_layout.addRow("模型名称:", self.custom_model_name)

        self.custom_temperature = QSpinBox()
        self.custom_temperature.setRange(0, 20)
        self.custom_temperature.setValue(7)
        self.custom_temperature.setSuffix(" / 10")
        self.custom_temperature.setStyleSheet(get_style("spinbox"))
        self.custom_temperature.setEnabled(False)
        placeholder_layout.addRow("温度:", self.custom_temperature)

        layout.addWidget(placeholder_group)
        layout.addStretch()

        self.tab_widget.addTab(tab, "自定义配置")
        
    def _load_default_config(self):
        """加载默认配置"""
        # 初始化时不需要设置默认值，因为我们使用新的模型选择方式
        pass

    def _load_available_models(self):
        """加载可用模型列表"""
        # 显示加载状态
        self.loading_label.setVisible(True)
        self.progress_bar.setVisible(True)
        self.models_list.setVisible(False)
        self.error_label.setVisible(False)
        self.reload_btn.setVisible(False)

        # 创建并启动工作线程
        self.model_worker = ModelFetchWorker(self.api_base_url)
        self.model_worker.models_fetched.connect(self._on_models_loaded)
        self.model_worker.error_occurred.connect(self._on_models_error)
        self.model_worker.start()

    def _on_models_loaded(self, models_data: Dict[str, Any]):
        """处理模型加载成功"""
        try:
            # 隐藏加载状态
            self.loading_label.setVisible(False)
            self.progress_bar.setVisible(False)

            # 提取模型列表 - 现在期望直接是数组格式
            models = models_data if isinstance(models_data, list) else models_data.get("models", [])
            if not models:
                self._show_error("未找到可用的模型")
                return

            # 存储模型数据
            self.available_models = models

            # 填充下拉列表
            self.models_list.clear()
            for model in models:
                if isinstance(model, dict):
                    # 使用display_name作为显示名称
                    display_name = model.get("display_name", model.get("model_name", str(model)))
                    self.models_list.addItem(display_name)
                else:
                    # 如果模型是字符串（向后兼容）
                    self.models_list.addItem(str(model))

            # 设置第一个模型为默认选择
            if self.models_list.count() > 0:
                self.models_list.setCurrentIndex(0)
                self.selected_model = models[0]
                self._update_model_details(models[0])

            # 显示模型列表
            self.models_list.setVisible(True)

        except Exception as e:
            self._show_error(f"处理模型数据时出错: {str(e)}")

    def _on_models_error(self, error_message: str):
        """处理模型加载错误"""
        self._show_error(f"加载模型列表失败: {error_message}")

    def _show_error(self, message: str):
        """显示错误信息"""
        self.loading_label.setVisible(False)
        self.progress_bar.setVisible(False)
        self.models_list.setVisible(False)

        self.error_label.setText(message)
        self.error_label.setVisible(True)
        self.reload_btn.setVisible(True)

    def _on_model_selected(self, display_name: str):
        """处理模型选择变化"""
        if not display_name or not self.available_models:
            return

        # 查找选中的模型数据 - 基于display_name匹配
        selected_model = None
        for model in self.available_models:
            if isinstance(model, dict):
                model_display_name = model.get("display_name", model.get("model_name", str(model)))
                if model_display_name == display_name:
                    selected_model = model
                    break
            elif str(model) == display_name:
                selected_model = model
                break

        if selected_model:
            self.selected_model = selected_model
            self._update_model_details(selected_model)

    def _update_model_details(self, model):
        """更新模型详情显示"""
        if isinstance(model, dict):
            details = []

            # 显示技术模型名称
            model_name = model.get('model_name', '未知')
            details.append(f"模型标识: {model_name}")

            # 显示提供商
            provider = model.get('provider', '未知')
            details.append(f"提供商: {provider}")

            # 如果有其他字段也可以显示
            if 'description' in model:
                details.append(f"描述: {model['description']}")

            self.model_details.setPlainText("\n".join(details))
        else:
            # 向后兼容字符串格式
            self.model_details.setPlainText(f"模型: {str(model)}")

    def get_selected_model(self) -> Optional[Dict[str, Any]]:
        """获取当前选中的模型"""
        return self.selected_model
        
    def _test_connection(self):
        """测试连接"""
        if not self.selected_model:
            QMessageBox.warning(self, "测试连接", "请先选择一个模型")
            return

        # 显示测试中状态
        self.test_btn.setEnabled(False)
        self.test_btn.setText("测试中...")

        try:
            # 创建客户端并测试连接
            client = HedgeFundClient(self.api_base_url)
            if client.health_check():
                QMessageBox.information(self, "测试连接", "连接测试成功！")
            else:
                QMessageBox.warning(self, "测试连接", "连接测试失败，请检查API服务器状态")
        except Exception as e:
            QMessageBox.critical(self, "测试连接", f"连接测试出错: {str(e)}")
        finally:
            # 恢复按钮状态
            self.test_btn.setEnabled(True)
            self.test_btn.setText("测试连接")

    def _save_config(self):
        """保存配置"""
        if not self.selected_model:
            QMessageBox.warning(self, "保存配置", "请先选择一个模型")
            return

        # 构建配置数据
        config = {
            "selected_model": self.selected_model,
            "api_base_url": self.api_base_url,
            "tab_index": self.tab_widget.currentIndex()
        }

        # 发出配置变化信号
        self.config_changed.emit(config)

        # 显示保存成功消息
        QMessageBox.information(self, "保存配置", "LLM配置已保存成功！")
        
    def load_config(self, config: Dict[str, Any]):
        """加载配置"""
        try:
            # 加载选中的模型
            if "selected_model" in config:
                self.selected_model = config["selected_model"]

                # 如果模型列表已加载，更新选择
                if self.available_models and self.models_list.count() > 0:
                    display_name = ""
                    if isinstance(self.selected_model, dict):
                        display_name = self.selected_model.get("display_name", self.selected_model.get("model_name", ""))
                    else:
                        display_name = str(self.selected_model)

                    # 在下拉列表中查找并选择对应的模型
                    for i in range(self.models_list.count()):
                        if self.models_list.itemText(i) == display_name:
                            self.models_list.setCurrentIndex(i)
                            break

                    self._update_model_details(self.selected_model)

            # 加载API基础URL
            if "api_base_url" in config:
                self.api_base_url = config["api_base_url"]

            # 加载标签页索引
            if "tab_index" in config:
                tab_index = config.get("tab_index", 0)
                if 0 <= tab_index < self.tab_widget.count():
                    self.tab_widget.setCurrentIndex(tab_index)

        except Exception as e:
            print(f"加载LLM配置时出错: {str(e)}")
            
    def refresh_styles(self):
        """刷新样式"""
        # 刷新标题样式
        title = self.findChild(QLabel)
        if title and title.text() == "LLM配置":
            title.setStyleSheet(get_style("title"))

        # 刷新标签页样式
        self.tab_widget.setStyleSheet(get_style("tab"))

        # 刷新输入框样式
        for child in self.findChildren(QLineEdit):
            child.setStyleSheet(get_style("input"))

        # 刷新下拉框样式
        for child in self.findChildren(QComboBox):
            child.setStyleSheet(get_style("combobox"))

        # 刷新数字输入框样式
        for child in self.findChildren(QSpinBox):
            child.setStyleSheet(get_style("spinbox"))

        # 刷新复选框样式
        for child in self.findChildren(QCheckBox):
            child.setStyleSheet(get_style("checkbox"))

        # 刷新分组框样式
        for child in self.findChildren(QGroupBox):
            child.setStyleSheet(get_style("groupbox"))

        # 刷新文本编辑器样式
        for child in self.findChildren(QTextEdit):
            child.setStyleSheet(get_style("input"))

        # 刷新进度条样式
        if hasattr(self, 'progress_bar'):
            self.progress_bar.setStyleSheet(get_style("progress_bar"))

        # 刷新按钮样式
        self.test_btn.setStyleSheet(get_style("button_secondary"))
        self.save_btn.setStyleSheet(get_style("button_primary"))

        if hasattr(self, 'reload_btn'):
            self.reload_btn.setStyleSheet(get_style("button_secondary"))