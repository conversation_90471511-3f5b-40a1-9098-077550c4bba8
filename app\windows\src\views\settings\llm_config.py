import sys
import os
from PyQt6.QtWidgets import (
    QW<PERSON>t, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit,
    QPushButton, QComboBox, QSpinBox, QCheckBox, QGroupBox,
    QFormLayout, QTextEdit, QTabWidget
)
from PyQt6.QtCore import Qt, pyqtSignal
from typing import Dict, Any

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from styles import get_style

class LLMConfigView(QWidget):
    """LLM配置页面"""
    
    config_changed = pyqtSignal(dict)  # 配置变化信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self._init_ui()
        self._load_default_config()
        
    def _init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        
        # 标题
        title = QLabel("LLM配置")
        title.setStyleSheet(get_style("title"))
        layout.addWidget(title)
        
        # 创建标签页
        self.tab_widget = QTabWidget()
        self.tab_widget.setStyleSheet(get_style("tab"))
        
        # OpenAI配置标签页
        self._create_openai_tab()
        
        # 本地模型配置标签页
        self._create_local_model_tab()
        
        # 其他API配置标签页
        self._create_other_api_tab()
        
        layout.addWidget(self.tab_widget)
        
        # 操作按钮
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        self.test_btn = QPushButton("测试连接")
        self.test_btn.setStyleSheet(get_style("button_secondary"))
        self.test_btn.clicked.connect(self._test_connection)
        button_layout.addWidget(self.test_btn)
        
        self.save_btn = QPushButton("保存配置")
        self.save_btn.setStyleSheet(get_style("button_primary"))
        self.save_btn.clicked.connect(self._save_config)
        button_layout.addWidget(self.save_btn)
        
        layout.addLayout(button_layout)
        
    def _create_openai_tab(self):
        """创建OpenAI配置标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # API密钥配置
        api_group = QGroupBox("API配置")
        api_group.setStyleSheet(get_style("groupbox"))
        api_layout = QFormLayout(api_group)
        
        self.openai_api_key = QLineEdit()
        self.openai_api_key.setEchoMode(QLineEdit.EchoMode.Password)
        self.openai_api_key.setPlaceholderText("输入OpenAI API密钥")
        self.openai_api_key.setStyleSheet(get_style("input"))
        api_layout.addRow("API密钥:", self.openai_api_key)
        
        self.openai_base_url = QLineEdit()
        self.openai_base_url.setPlaceholderText("https://api.openai.com/v1")
        self.openai_base_url.setStyleSheet(get_style("input"))
        api_layout.addRow("API地址:", self.openai_base_url)
        
        layout.addWidget(api_group)
        
        # 模型配置
        model_group = QGroupBox("模型配置")
        model_group.setStyleSheet(get_style("groupbox"))
        model_layout = QFormLayout(model_group)
        
        self.openai_model = QComboBox()
        self.openai_model.addItems([
            "gpt-4",
            "gpt-4-turbo",
            "gpt-3.5-turbo",
            "gpt-3.5-turbo-16k"
        ])
        self.openai_model.setStyleSheet(get_style("combobox"))
        model_layout.addRow("模型:", self.openai_model)
        
        self.openai_temperature = QSpinBox()
        self.openai_temperature.setRange(0, 20)
        self.openai_temperature.setValue(7)
        self.openai_temperature.setSuffix(" / 10")
        self.openai_temperature.setStyleSheet(get_style("spinbox"))
        model_layout.addRow("温度:", self.openai_temperature)
        
        self.openai_max_tokens = QSpinBox()
        self.openai_max_tokens.setRange(100, 8000)
        self.openai_max_tokens.setValue(2000)
        self.openai_max_tokens.setStyleSheet(get_style("spinbox"))
        model_layout.addRow("最大Token:", self.openai_max_tokens)
        
        layout.addWidget(model_group)
        
        # 代理配置
        proxy_group = QGroupBox("代理配置")
        proxy_group.setStyleSheet(get_style("groupbox"))
        proxy_layout = QFormLayout(proxy_group)
        
        self.use_proxy = QCheckBox("使用代理")
        self.use_proxy.setStyleSheet(get_style("checkbox"))
        proxy_layout.addRow(self.use_proxy)
        
        self.proxy_url = QLineEdit()
        self.proxy_url.setPlaceholderText("http://127.0.0.1:7890")
        self.proxy_url.setStyleSheet(get_style("input"))
        proxy_layout.addRow("代理地址:", self.proxy_url)
        
        layout.addWidget(proxy_group)
        layout.addStretch()
        
        self.tab_widget.addTab(tab, "OpenAI")
        
    def _create_local_model_tab(self):
        """创建本地模型配置标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Ollama配置
        ollama_group = QGroupBox("Ollama配置")
        ollama_group.setStyleSheet(get_style("groupbox"))
        ollama_layout = QFormLayout(ollama_group)
        
        self.ollama_url = QLineEdit()
        self.ollama_url.setPlaceholderText("http://localhost:11434")
        self.ollama_url.setStyleSheet(get_style("input"))
        ollama_layout.addRow("Ollama地址:", self.ollama_url)
        
        self.ollama_model = QComboBox()
        self.ollama_model.setEditable(True)
        self.ollama_model.addItems([
            "llama2",
            "llama2:13b",
            "llama2:70b",
            "codellama",
            "mistral",
            "qwen2"
        ])
        self.ollama_model.setStyleSheet(get_style("combobox"))
        ollama_layout.addRow("模型:", self.ollama_model)
        
        layout.addWidget(ollama_group)
        
        # 本地模型参数
        params_group = QGroupBox("模型参数")
        params_group.setStyleSheet(get_style("groupbox"))
        params_layout = QFormLayout(params_group)
        
        self.local_temperature = QSpinBox()
        self.local_temperature.setRange(0, 20)
        self.local_temperature.setValue(7)
        self.local_temperature.setSuffix(" / 10")
        self.local_temperature.setStyleSheet(get_style("spinbox"))
        params_layout.addRow("温度:", self.local_temperature)
        
        self.local_max_tokens = QSpinBox()
        self.local_max_tokens.setRange(100, 8000)
        self.local_max_tokens.setValue(2000)
        self.local_max_tokens.setStyleSheet(get_style("spinbox"))
        params_layout.addRow("最大Token:", self.local_max_tokens)
        
        layout.addWidget(params_group)
        layout.addStretch()
        
        self.tab_widget.addTab(tab, "本地模型")
        
    def _create_other_api_tab(self):
        """创建其他API配置标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 其他API配置
        api_group = QGroupBox("其他API配置")
        api_group.setStyleSheet(get_style("groupbox"))
        api_layout = QFormLayout(api_group)
        
        self.other_api_type = QComboBox()
        self.other_api_type.addItems([
            "Claude (Anthropic)",
            "Gemini (Google)",
            "Qwen (阿里云)",
            "自定义"
        ])
        self.other_api_type.setStyleSheet(get_style("combobox"))
        api_layout.addRow("API类型:", self.other_api_type)
        
        self.other_api_key = QLineEdit()
        self.other_api_key.setEchoMode(QLineEdit.EchoMode.Password)
        self.other_api_key.setPlaceholderText("输入API密钥")
        self.other_api_key.setStyleSheet(get_style("input"))
        api_layout.addRow("API密钥:", self.other_api_key)
        
        self.other_api_url = QLineEdit()
        self.other_api_url.setPlaceholderText("输入API地址")
        self.other_api_url.setStyleSheet(get_style("input"))
        api_layout.addRow("API地址:", self.other_api_url)
        
        layout.addWidget(api_group)
        layout.addStretch()
        
        self.tab_widget.addTab(tab, "其他API")
        
    def _load_default_config(self):
        """加载默认配置"""
        # 设置默认值
        self.openai_base_url.setText("https://api.openai.com/v1")
        self.openai_model.setCurrentText("gpt-4")
        self.ollama_url.setText("http://localhost:11434")
        self.ollama_model.setCurrentText("llama2")
        
    def _test_connection(self):
        """测试连接"""
        # TODO: 实现连接测试逻辑
        print("测试LLM连接...")
        
    def _save_config(self):
        """保存配置"""
        config = {
            "openai": {
                "api_key": self.openai_api_key.text(),
                "base_url": self.openai_base_url.text(),
                "model": self.openai_model.currentText(),
                "temperature": self.openai_temperature.value() / 10,
                "max_tokens": self.openai_max_tokens.value(),
                "use_proxy": self.use_proxy.isChecked(),
                "proxy_url": self.proxy_url.text()
            },
            "ollama": {
                "url": self.ollama_url.text(),
                "model": self.ollama_model.currentText(),
                "temperature": self.local_temperature.value() / 10,
                "max_tokens": self.local_max_tokens.value()
            },
            "other": {
                "type": self.other_api_type.currentText(),
                "api_key": self.other_api_key.text(),
                "api_url": self.other_api_url.text()
            }
        }
        
        self.config_changed.emit(config)
        print("LLM配置已保存")
        
    def load_config(self, config: Dict[str, Any]):
        """加载配置"""
        if "openai" in config:
            openai_config = config["openai"]
            self.openai_api_key.setText(openai_config.get("api_key", ""))
            self.openai_base_url.setText(openai_config.get("base_url", "https://api.openai.com/v1"))
            self.openai_model.setCurrentText(openai_config.get("model", "gpt-4"))
            self.openai_temperature.setValue(int(openai_config.get("temperature", 0.7) * 10))
            self.openai_max_tokens.setValue(openai_config.get("max_tokens", 2000))
            self.use_proxy.setChecked(openai_config.get("use_proxy", False))
            self.proxy_url.setText(openai_config.get("proxy_url", ""))
            
        if "ollama" in config:
            ollama_config = config["ollama"]
            self.ollama_url.setText(ollama_config.get("url", "http://localhost:11434"))
            self.ollama_model.setCurrentText(ollama_config.get("model", "llama2"))
            self.local_temperature.setValue(int(ollama_config.get("temperature", 0.7) * 10))
            self.local_max_tokens.setValue(ollama_config.get("max_tokens", 2000))
            
        if "other" in config:
            other_config = config["other"]
            self.other_api_type.setCurrentText(other_config.get("type", "Claude (Anthropic)"))
            self.other_api_key.setText(other_config.get("api_key", ""))
            self.other_api_url.setText(other_config.get("api_url", ""))
            
    def refresh_styles(self):
        """刷新样式"""
        title = self.findChild(QLabel)
        if title:
            title.setStyleSheet(get_style("title"))
            
        self.tab_widget.setStyleSheet(get_style("tab"))
        
        for child in self.findChildren(QLineEdit):
            child.setStyleSheet(get_style("input"))
            
        for child in self.findChildren(QComboBox):
            child.setStyleSheet(get_style("combobox"))
            
        for child in self.findChildren(QSpinBox):
            child.setStyleSheet(get_style("spinbox"))
            
        for child in self.findChildren(QCheckBox):
            child.setStyleSheet(get_style("checkbox"))
            
        for child in self.findChildren(QGroupBox):
            child.setStyleSheet(get_style("groupbox"))
            
        self.test_btn.setStyleSheet(get_style("button_secondary"))
        self.save_btn.setStyleSheet(get_style("button_primary")) 