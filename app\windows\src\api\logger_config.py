"""
API日志配置管理器
提供简单的接口来控制API调试日志
"""

import os
import json
from typing import Dict, Any
from .client import APILogger


class LoggerConfig:
    """API日志配置管理器"""
    
    def __init__(self):
        self.logger = APILogger()

        # 尝试导入ConfigManager，如果失败则使用独立配置
        try:
            from utils.config_manager import get_config_manager
            self.config_manager = get_config_manager()
            self.use_config_manager = True
        except ImportError:
            # 后备方案：使用独立配置文件
            self.config_dir = os.path.join(os.path.expanduser("~"), "topai")
            self.config_file = os.path.join(self.config_dir, "logger_config.json")
            os.makedirs(self.config_dir, exist_ok=True)
            self.use_config_manager = False

        # 加载配置
        self.load_config()
    
    def load_config(self):
        """加载配置文件"""
        default_config = {
            "debug_enabled": True,
            "console_enabled": True,
            "file_enabled": True,
            "max_file_size_mb": 10,
            "max_files": 5,
            "cleanup_days": 7
        }

        try:
            if self.use_config_manager:
                # 使用ConfigManager
                config = self.config_manager.get_logging_config()
                # 合并默认配置
                for key, value in default_config.items():
                    if key not in config:
                        config[key] = value
            else:
                # 使用独立配置文件
                if os.path.exists(self.config_file):
                    with open(self.config_file, 'r', encoding='utf-8') as f:
                        config = json.load(f)
                        # 合并默认配置
                        for key, value in default_config.items():
                            if key not in config:
                                config[key] = value
                else:
                    config = default_config
                    self.save_config(config)

            # 应用配置
            self.apply_config(config)

        except Exception as e:
            print(f"加载日志配置失败，使用默认配置: {e}")
            self.apply_config(default_config)
    
    def save_config(self, config: Dict[str, Any]):
        """保存配置文件"""
        try:
            if self.use_config_manager:
                # 使用ConfigManager保存
                self.config_manager.set_logging_config(config)
            else:
                # 使用独立配置文件
                with open(self.config_file, 'w', encoding='utf-8') as f:
                    json.dump(config, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"保存日志配置失败: {e}")
    
    def apply_config(self, config: Dict[str, Any]):
        """应用配置"""
        self.logger.enable_debug(config.get("debug_enabled", True))
        self.logger.enable_console(config.get("console_enabled", True))
        self.logger.enable_file_logging(config.get("file_enabled", True))
        
        # 更新日志器配置
        self.logger.max_file_size = config.get("max_file_size_mb", 10) * 1024 * 1024
        self.logger.max_files = config.get("max_files", 5)
        self.logger.cleanup_days = config.get("cleanup_days", 7)
    
    def enable_debug(self, enabled: bool = True):
        """启用/禁用调试模式"""
        self.logger.enable_debug(enabled)
        self._update_config("debug_enabled", enabled)
    
    def enable_console(self, enabled: bool = True):
        """启用/禁用控制台输出"""
        self.logger.enable_console(enabled)
        self._update_config("console_enabled", enabled)
    
    def enable_file_logging(self, enabled: bool = True):
        """启用/禁用文件日志"""
        self.logger.enable_file_logging(enabled)
        self._update_config("file_enabled", enabled)
    
    def set_file_size_limit(self, size_mb: int):
        """设置单个日志文件大小限制（MB）"""
        self.logger.max_file_size = size_mb * 1024 * 1024
        self._update_config("max_file_size_mb", size_mb)
    
    def set_max_files(self, count: int):
        """设置最大日志文件数量"""
        self.logger.max_files = count
        self._update_config("max_files", count)
    
    def set_cleanup_days(self, days: int):
        """设置日志清理天数"""
        self.logger.cleanup_days = days
        self._update_config("cleanup_days", days)
    
    def _update_config(self, key: str, value: Any):
        """更新配置项"""
        try:
            if self.use_config_manager:
                # 使用ConfigManager
                config = self.config_manager.get_logging_config()
                config[key] = value
                self.config_manager.set_logging_config(config)
            else:
                # 使用独立配置文件
                if os.path.exists(self.config_file):
                    with open(self.config_file, 'r', encoding='utf-8') as f:
                        config = json.load(f)
                else:
                    config = {}

                config[key] = value
                self.save_config(config)

        except Exception as e:
            print(f"更新配置失败: {e}")

    def get_config(self) -> Dict[str, Any]:
        """获取当前配置"""
        try:
            if self.use_config_manager:
                return self.config_manager.get_logging_config()
            else:
                if os.path.exists(self.config_file):
                    with open(self.config_file, 'r', encoding='utf-8') as f:
                        return json.load(f)
                else:
                    return {}
        except Exception as e:
            print(f"读取配置失败: {e}")
            return {}
    
    def reset_to_default(self):
        """重置为默认配置"""
        default_config = {
            "debug_enabled": True,
            "console_enabled": True,
            "file_enabled": True,
            "max_file_size_mb": 10,
            "max_files": 5,
            "cleanup_days": 7
        }
        
        self.save_config(default_config)
        self.apply_config(default_config)
        print("日志配置已重置为默认值")
    
    def disable_all_logging(self):
        """一键关闭所有日志"""
        self.enable_debug(False)
        self.enable_console(False)
        self.enable_file_logging(False)
        print("所有API日志已关闭")
    
    def enable_minimal_logging(self):
        """启用最小日志（仅错误）"""
        self.enable_debug(True)
        self.enable_console(True)
        self.enable_file_logging(False)
        # 设置日志级别为ERROR
        self.logger.logger.setLevel(40)  # ERROR level
        print("已启用最小日志模式（仅错误信息）")
    
    def enable_full_logging(self):
        """启用完整日志"""
        self.enable_debug(True)
        self.enable_console(True)
        self.enable_file_logging(True)
        # 设置日志级别为DEBUG
        self.logger.logger.setLevel(10)  # DEBUG level
        print("已启用完整日志模式")
    
    def get_log_directory(self) -> str:
        """获取日志目录路径"""
        return self.logger.log_dir
    
    def get_log_files(self) -> list:
        """获取所有日志文件列表"""
        log_files = []
        if os.path.exists(self.logger.log_dir):
            for filename in os.listdir(self.logger.log_dir):
                if filename.startswith("api_client.log"):
                    file_path = os.path.join(self.logger.log_dir, filename)
                    file_size = os.path.getsize(file_path)
                    log_files.append({
                        "name": filename,
                        "path": file_path,
                        "size": file_size,
                        "size_mb": round(file_size / 1024 / 1024, 2)
                    })
        return log_files
    
    def clear_all_logs(self):
        """清空所有日志文件"""
        try:
            log_files = self.get_log_files()
            for log_file in log_files:
                os.remove(log_file["path"])
            print(f"已清空 {len(log_files)} 个日志文件")
        except Exception as e:
            print(f"清空日志文件失败: {e}")


# 全局配置实例
logger_config = LoggerConfig()


# 便捷函数
def enable_api_debug(enabled: bool = True):
    """一键启用/禁用API调试"""
    logger_config.enable_debug(enabled)


def disable_all_api_logging():
    """一键关闭所有API日志"""
    logger_config.disable_all_logging()


def enable_minimal_api_logging():
    """启用最小API日志"""
    logger_config.enable_minimal_logging()


def enable_full_api_logging():
    """启用完整API日志"""
    logger_config.enable_full_logging()


def get_api_log_directory():
    """获取API日志目录"""
    return logger_config.get_log_directory()


def clear_api_logs():
    """清空所有API日志"""
    logger_config.clear_all_logs()
