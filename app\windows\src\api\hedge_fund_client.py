"""
对冲基金API客户端
提供与后端对冲基金API的通信接口
"""

import json
import asyncio
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime, timedelta
from .client import APIClient


class HedgeFundClient(APIClient):
    """对冲基金API客户端类"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        """
        初始化对冲基金API客户端
        
        Args:
            base_url: API服务器的基础URL
        """
        super().__init__(base_url)
        self.base_endpoint = "/hedge-fund"
    
    def get_agents(self) -> Dict[str, Any]:
        """
        获取可用的分析师代理列表
        
        Returns:
            Dict[str, Any]: 代理列表数据
        """
        try:
            response = self.get(f"{self.base_endpoint}/agents")
            return response
        except Exception as e:
            print(f"获取代理列表失败: {str(e)}")
            return {"agents": []}
    
    def get_language_models(self) -> Dict[str, Any]:
        """
        获取可用的语言模型列表
        
        Returns:
            Dict[str, Any]: 语言模型列表数据
        """
        try:
            response = self.get(f"{self.base_endpoint}/language-models")
            return response
        except Exception as e:
            print(f"获取语言模型列表失败: {str(e)}")
            return {"models": []}
    
    def run_hedge_fund_sync(
        self,
        tickers: List[str],
        selected_agents: List[str],
        agent_models: Optional[List[Dict[str, Any]]] = None,
        end_date: Optional[str] = None,
        start_date: Optional[str] = None,
        model_name: str = "gpt-4o",
        model_provider: str = "openai",
        initial_cash: float = 100000.0,
        margin_requirement: float = 0.0
    ) -> Dict[str, Any]:
        """
        同步运行对冲基金分析
        
        Args:
            tickers: 股票代码列表
            selected_agents: 选中的分析师代理列表
            agent_models: 代理模型配置列表
            end_date: 结束日期 (YYYY-MM-DD)
            start_date: 开始日期 (YYYY-MM-DD)
            model_name: 模型名称
            model_provider: 模型提供商
            initial_cash: 初始资金
            margin_requirement: 保证金要求
            
        Returns:
            Dict[str, Any]: 分析结果
        """
        # 如果没有提供结束日期，使用当前日期
        if not end_date:
            end_date = datetime.now().strftime("%Y-%m-%d")
        
        # 构建请求数据
        request_data = {
            "tickers": tickers,
            "selected_agents": selected_agents,
            "model_name": model_name,
            "model_provider": model_provider,
            "initial_cash": initial_cash,
            "margin_requirement": margin_requirement,
            "end_date": end_date
        }
        
        if start_date:
            request_data["start_date"] = start_date
        
        if agent_models:
            request_data["agent_models"] = agent_models
        
        try:
            response = self.post(f"{self.base_endpoint}/run", request_data)
            return response
        except Exception as e:
            print(f"运行对冲基金分析失败: {str(e)}")
            return {"error": str(e)}
    
    def run_hedge_fund_streaming(
        self,
        tickers: List[str],
        selected_agents: List[str],
        agent_models: Optional[List[Dict[str, Any]]] = None,
        end_date: Optional[str] = None,
        start_date: Optional[str] = None,
        model_name: str = "gpt-4o",
        model_provider: str = "openai",
        initial_cash: float = 100000.0,
        margin_requirement: float = 0.0,
        progress_callback: Optional[Callable[[Dict[str, Any]], None]] = None,
        complete_callback: Optional[Callable[[Dict[str, Any]], None]] = None,
        error_callback: Optional[Callable[[str], None]] = None
    ) -> Dict[str, Any]:
        """
        流式运行对冲基金分析
        
        Args:
            tickers: 股票代码列表
            selected_agents: 选中的分析师代理列表
            agent_models: 代理模型配置列表
            end_date: 结束日期 (YYYY-MM-DD)
            start_date: 开始日期 (YYYY-MM-DD)
            model_name: 模型名称
            model_provider: 模型提供商
            initial_cash: 初始资金
            margin_requirement: 保证金要求
            progress_callback: 进度回调函数
            complete_callback: 完成回调函数
            error_callback: 错误回调函数
            
        Returns:
            Dict[str, Any]: 最终分析结果
        """
        # 如果没有提供结束日期，使用当前日期
        if not end_date:
            end_date = datetime.now().strftime("%Y-%m-%d")
        
        # 构建请求数据
        request_data = {
            "tickers": tickers,
            "selected_agents": selected_agents,
            "model_name": model_name,
            "model_provider": model_provider,
            "initial_cash": initial_cash,
            "margin_requirement": margin_requirement,
            "end_date": end_date
        }
        
        if start_date:
            request_data["start_date"] = start_date
        
        if agent_models:
            request_data["agent_models"] = agent_models
        
        try:
            # 发送POST请求获取流式响应
            url = f"{self.base_url}{self.base_endpoint}/run"
            response = self.session.post(
                url,
                json=request_data,
                headers={'Accept': 'text/event-stream'},
                stream=True,
                timeout=300  # 5分钟超时
            )
            
            response.raise_for_status()
            
            final_result = {}
            
            # 处理流式响应
            for line in response.iter_lines(decode_unicode=True):
                if line.startswith('event: '):
                    event_type = line[7:]  # 移除 'event: ' 前缀
                elif line.startswith('data: '):
                    data_str = line[6:]  # 移除 'data: ' 前缀
                    try:
                        data = json.loads(data_str)
                        
                        if event_type == 'start':
                            if progress_callback:
                                progress_callback({"type": "start", "data": data})
                        
                        elif event_type == 'progress':
                            if progress_callback:
                                progress_callback({"type": "progress", "data": data})
                        
                        elif event_type == 'error':
                            error_msg = data.get('message', '未知错误')
                            if error_callback:
                                error_callback(error_msg)
                            else:
                                print(f"分析错误: {error_msg}")
                        
                        elif event_type == 'complete':
                            final_result = data.get('data', {})
                            if complete_callback:
                                complete_callback(final_result)
                            break
                    
                    except json.JSONDecodeError:
                        print(f"解析响应数据失败: {data_str}")
                        continue
            
            return final_result
            
        except Exception as e:
            error_msg = f"运行对冲基金分析失败: {str(e)}"
            if error_callback:
                error_callback(error_msg)
            else:
                print(error_msg)
            return {"error": error_msg}
    
    def validate_hedge_fund_request(
        self,
        tickers: List[str],
        selected_agents: List[str],
        end_date: Optional[str] = None,
        start_date: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        验证对冲基金请求参数
        
        Args:
            tickers: 股票代码列表
            selected_agents: 选中的分析师代理列表
            end_date: 结束日期
            start_date: 开始日期
            
        Returns:
            Dict[str, Any]: 验证结果
        """
        errors = []
        
        # 验证股票代码
        if not tickers:
            errors.append("股票代码列表不能为空")
        elif len(tickers) > 10:  # 限制最大股票数量
            errors.append("股票代码数量不能超过10个")
        
        # 验证分析师代理
        if not selected_agents:
            errors.append("分析师代理列表不能为空")
        
        # 验证日期
        if end_date:
            try:
                datetime.strptime(end_date, "%Y-%m-%d")
            except ValueError:
                errors.append("结束日期格式无效，应为YYYY-MM-DD")
        
        if start_date:
            try:
                datetime.strptime(start_date, "%Y-%m-%d")
            except ValueError:
                errors.append("开始日期格式无效，应为YYYY-MM-DD")
        
        # 验证日期范围
        if start_date and end_date:
            try:
                start_dt = datetime.strptime(start_date, "%Y-%m-%d")
                end_dt = datetime.strptime(end_date, "%Y-%m-%d")
                if start_dt >= end_dt:
                    errors.append("开始日期必须早于结束日期")
                if (end_dt - start_dt).days > 365:
                    errors.append("分析时间范围不能超过一年")
            except ValueError:
                pass  # 日期格式错误已在上面处理
        
        return {
            "is_valid": len(errors) == 0,
            "errors": errors
        }
    
    def get_default_analysis_period(self, end_date: Optional[str] = None) -> Dict[str, str]:
        """
        获取默认分析时间段
        
        Args:
            end_date: 结束日期，如果为None则使用当前日期
            
        Returns:
            Dict[str, str]: 包含开始和结束日期的字典
        """
        if not end_date:
            end_date = datetime.now().strftime("%Y-%m-%d")
        
        end_dt = datetime.strptime(end_date, "%Y-%m-%d")
        start_dt = end_dt - timedelta(days=90)  # 默认90天
        
        return {
            "start_date": start_dt.strftime("%Y-%m-%d"),
            "end_date": end_date
        } 