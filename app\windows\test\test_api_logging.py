#!/usr/bin/env python3
"""
API日志功能测试脚本
"""

import sys
import os
import time

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 添加src目录到Python路径
src_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "src")
if src_dir not in sys.path:
    sys.path.insert(0, src_dir)

try:
    from api.client import APIClient
    from api.hedge_fund_client import HedgeFundClient
    from api.logger_config import (
        logger_config, 
        enable_api_debug, 
        disable_all_api_logging,
        enable_minimal_api_logging,
        enable_full_api_logging,
        get_api_log_directory,
        clear_api_logs
    )

    def test_basic_api_logging():
        """测试基础API日志功能"""
        print("=== 测试基础API日志功能 ===")
        
        # 创建API客户端
        client = APIClient("http://localhost:8000", debug=True)
        
        print("1. 测试健康检查请求...")
        result = client.health_check()
        print(f"健康检查结果: {result}")
        
        print("\n2. 测试GET请求...")
        try:
            response = client.get("/test-endpoint")
            print(f"GET响应: {response}")
        except Exception as e:
            print(f"GET请求失败（预期）: {e}")
        
        print("\n3. 测试POST请求...")
        try:
            response = client.post("/test-endpoint", {"test": "data"})
            print(f"POST响应: {response}")
        except Exception as e:
            print(f"POST请求失败（预期）: {e}")
        
        client.close()

    def test_hedge_fund_client_logging():
        """测试对冲基金客户端日志功能"""
        print("\n=== 测试对冲基金客户端日志功能 ===")
        
        # 创建对冲基金客户端
        hf_client = HedgeFundClient("http://localhost:8000", debug=True)
        
        print("1. 测试获取代理列表...")
        try:
            agents = hf_client.get_agents()
            print(f"代理数量: {len(agents.get('agents', []))}")
        except Exception as e:
            print(f"获取代理失败: {e}")
        
        print("\n2. 测试获取语言模型...")
        try:
            models = hf_client.get_language_models()
            print(f"模型数量: {len(models.get('models', []))}")
        except Exception as e:
            print(f"获取模型失败: {e}")
        
        print("\n3. 测试分析请求（模拟）...")
        try:
            result = hf_client.run_hedge_fund_sync(
                tickers=["AAPL"],
                selected_agents=["warren_buffett"],
                start_date="2024-01-01",
                end_date="2024-01-31"
            )
            print(f"分析结果类型: {type(result)}")
        except Exception as e:
            print(f"分析请求失败（预期）: {e}")
        
        hf_client.close()

    def test_logging_controls():
        """测试日志控制功能"""
        print("\n=== 测试日志控制功能 ===")
        
        print("1. 当前日志目录:", get_api_log_directory())
        
        print("\n2. 当前配置:")
        config = logger_config.get_config()
        for key, value in config.items():
            print(f"  {key}: {value}")
        
        print("\n3. 测试日志文件列表:")
        log_files = logger_config.get_log_files()
        for log_file in log_files:
            print(f"  {log_file['name']}: {log_file['size_mb']} MB")
        
        print("\n4. 测试一键关闭日志...")
        disable_all_api_logging()
        
        # 创建客户端测试（应该没有日志输出）
        client = APIClient("http://localhost:8000")
        client.health_check()
        client.close()
        
        print("\n5. 重新启用完整日志...")
        enable_full_api_logging()
        
        # 再次测试（应该有日志输出）
        client = APIClient("http://localhost:8000")
        client.health_check()
        client.close()

    def test_streaming_logging():
        """测试流式请求日志"""
        print("\n=== 测试流式请求日志 ===")
        
        hf_client = HedgeFundClient("http://localhost:8000", debug=True)
        
        def progress_callback(data):
            print(f"进度回调: {data.get('type', 'unknown')}")
        
        def complete_callback(data):
            print(f"完成回调: 数据键 {list(data.keys()) if isinstance(data, dict) else type(data)}")
        
        def error_callback(error):
            print(f"错误回调: {error}")
        
        print("发送流式分析请求（模拟）...")
        try:
            result = hf_client.run_hedge_fund_streaming(
                tickers=["AAPL"],
                selected_agents=["warren_buffett"],
                start_date="2024-01-01",
                end_date="2024-01-31",
                progress_callback=progress_callback,
                complete_callback=complete_callback,
                error_callback=error_callback
            )
            print(f"流式请求结果: {type(result)}")
        except Exception as e:
            print(f"流式请求失败（预期）: {e}")
        
        hf_client.close()

    def test_log_management():
        """测试日志管理功能"""
        print("\n=== 测试日志管理功能 ===")
        
        print("1. 设置日志配置...")
        logger_config.set_file_size_limit(5)  # 5MB
        logger_config.set_max_files(3)
        logger_config.set_cleanup_days(3)
        
        print("2. 生成一些日志...")
        client = APIClient("http://localhost:8000", debug=True)
        for i in range(5):
            client.health_check()
            time.sleep(0.1)
        client.close()
        
        print("3. 检查日志文件...")
        log_files = logger_config.get_log_files()
        total_size = sum(f['size'] for f in log_files)
        print(f"日志文件数量: {len(log_files)}")
        print(f"总大小: {total_size / 1024:.2f} KB")
        
        print("4. 测试最小日志模式...")
        enable_minimal_api_logging()
        client = APIClient("http://localhost:8000")
        client.health_check()
        client.close()
        
        print("5. 恢复完整日志...")
        enable_full_api_logging()

    def main():
        """主测试函数"""
        print("API日志功能测试开始")
        print("=" * 50)
        
        try:
            # 基础功能测试
            test_basic_api_logging()
            
            # 对冲基金客户端测试
            test_hedge_fund_client_logging()
            
            # 日志控制测试
            test_logging_controls()
            
            # 流式请求测试
            test_streaming_logging()
            
            # 日志管理测试
            test_log_management()
            
            print("\n" + "=" * 50)
            print("所有测试完成！")
            print(f"日志文件位置: {get_api_log_directory()}")
            print("\n使用说明:")
            print("1. 一键关闭所有日志: disable_all_api_logging()")
            print("2. 启用最小日志: enable_minimal_api_logging()")
            print("3. 启用完整日志: enable_full_api_logging()")
            print("4. 清空所有日志: clear_api_logs()")
            print("5. 查看日志目录: get_api_log_directory()")
            
        except Exception as e:
            print(f"测试过程中出现错误: {e}")
            import traceback
            traceback.print_exc()

    if __name__ == "__main__":
        main()

except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保已正确设置Python路径")
    sys.exit(1)
except Exception as e:
    print(f"运行错误: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
