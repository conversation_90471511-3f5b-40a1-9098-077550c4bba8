import sys
import os
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QLineEdit, QTextEdit
)
from PyQt6.QtCore import QTimer
from typing import List

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from .stock_input import StockInputView
from .date_range import DateRangeView
from ..common.results_view import ResultsView
from ..common.agent_selector import AgentSelector
from styles import get_style

class SingleStockSingleAgentPage(QWidget):
    """单股单顾问分析页面"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self._init_ui()
        # 状态变量
        self.current_stocks = []
        self.current_agents = []
        self.current_start_date = ""
        self.current_end_date = ""

    def _init_ui(self):
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        # 页面说明
        page_desc = QLabel("🎯 单股单顾问深度分析：选择一个AI投资顾问对单只股票进行深入分析，支持持续对话探讨投资策略和风险。")
        page_desc.setStyleSheet(get_style("page_description"))
        page_desc.setWordWrap(True)
        layout.addWidget(page_desc)
        # 第一行：股票选择、Agent选择和日期选择
        top_layout = QHBoxLayout()
        # 股票输入组件
        stock_group = QWidget()
        stock_layout = QVBoxLayout(stock_group)
        stock_title = QLabel("股票选择")
        stock_title.setStyleSheet(get_style("title"))
        stock_layout.addWidget(stock_title)
        self.stock_input = StockInputView()
        stock_layout.addWidget(self.stock_input)
        top_layout.addWidget(stock_group)
        # Agent选择组件（单选模式）
        agent_group = QWidget()
        agent_layout = QVBoxLayout(agent_group)
        agent_tips = QLabel("💡 选择一个AI投资顾问进行深度分析，支持持续对话探讨")
        agent_tips.setStyleSheet(get_style("tips"))
        agent_tips.setWordWrap(True)
        agent_layout.addWidget(agent_tips)
        self.agent_selector = AgentSelector(multi_select=False)
        agent_layout.addWidget(self.agent_selector)
        top_layout.addWidget(agent_group)
        # 日期范围组件
        date_group = QWidget()
        date_layout = QVBoxLayout(date_group)
        date_title = QLabel("时间范围")
        date_title.setStyleSheet(get_style("title"))
        date_layout.addWidget(date_title)
        self.date_range = DateRangeView()
        date_layout.addWidget(self.date_range)
        top_layout.addWidget(date_group)
        layout.addLayout(top_layout)
        # 第二行：开始分析按钮
        analyze_layout = QHBoxLayout()
        analyze_layout.addStretch()
        self.start_analyze_btn = QPushButton("开始分析")
        self.start_analyze_btn.setStyleSheet(get_style("start_analyze_button"))
        self.start_analyze_btn.setEnabled(False)
        analyze_layout.addWidget(self.start_analyze_btn)
        analyze_layout.addStretch()
        layout.addLayout(analyze_layout)
        # 第三行：结果显示和对话区域
        content_layout = QHBoxLayout()
        # 左侧：分析结果
        results_group = QWidget()
        results_layout = QVBoxLayout(results_group)
        results_title = QLabel("分析结果")
        results_title.setStyleSheet(get_style("title"))
        results_layout.addWidget(results_title)
        self.results_view = ResultsView()
        results_layout.addWidget(self.results_view)
        content_layout.addWidget(results_group)
        # 右侧：持续对话区域
        chat_group = QWidget()
        chat_layout = QVBoxLayout(chat_group)
        chat_title = QLabel("深度对话")
        chat_title.setStyleSheet(get_style("title"))
        chat_layout.addWidget(chat_title)
        chat_tips = QLabel("💬 与AI顾问进行深度对话，探讨投资策略、风险评估和具体操作建议")
        chat_tips.setStyleSheet(get_style("tips"))
        chat_tips.setWordWrap(True)
        chat_layout.addWidget(chat_tips)
        self.chat_history = QTextEdit()
        self.chat_history.setReadOnly(True)
        self.chat_history.setMinimumHeight(300)
        self.chat_history.setStyleSheet(get_style("text_edit"))
        chat_layout.addWidget(self.chat_history)
        chat_input_layout = QHBoxLayout()
        self.chat_input = QLineEdit()
        self.chat_input.setPlaceholderText("输入您的问题，与AI顾问深入探讨...")
        self.chat_input.setStyleSheet(get_style("input"))
        chat_input_layout.addWidget(self.chat_input)
        self.chat_send_btn = QPushButton("发送")
        self.chat_send_btn.setStyleSheet(get_style("button_primary"))
        chat_input_layout.addWidget(self.chat_send_btn)
        chat_layout.addLayout(chat_input_layout)
        content_layout.addWidget(chat_group)
        content_layout.setStretch(0, 2)
        content_layout.setStretch(1, 1)
        layout.addLayout(content_layout) 