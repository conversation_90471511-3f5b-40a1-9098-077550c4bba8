import sys
import os
from PyQt6.QtWidgets import (
    QW<PERSON>t, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QLineEdit, QTextEdit,
    QMessageBox, QProgressBar
)
from PyQt6.QtCore import QTimer, QThread, pyqtSignal
from typing import List, Dict, Any, Optional

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from .stock_input import StockInputView
from .date_range import DateRangeView
from ..common.results_view import ResultsView
from ..common.agent_selector import AgentSelector
from styles import get_style
from api import HedgeFundClient


class AnalysisWorker(QThread):
    """分析工作线程"""

    # 信号定义
    progress_updated = pyqtSignal(dict)  # 进度更新信号
    analysis_completed = pyqtSignal(dict)  # 分析完成信号
    error_occurred = pyqtSignal(str)  # 错误信号

    def __init__(self, tickers: List[str], agents: List[str], start_date: str, end_date: str,
                 api_base_url: str = "http://localhost:8000"):
        super().__init__()
        self.tickers = tickers
        self.agents = agents
        self.start_date = start_date
        self.end_date = end_date
        self.api_base_url = api_base_url
        self.client = HedgeFundClient(api_base_url)

    def run(self):
        """执行分析"""
        try:
            # 调用流式API
            result = self.client.run_hedge_fund_streaming(
                tickers=self.tickers,
                selected_agents=self.agents,
                start_date=self.start_date,
                end_date=self.end_date,
                progress_callback=self._on_progress,
                complete_callback=self._on_complete,
                error_callback=self._on_error
            )

            if "error" in result:
                self.error_occurred.emit(result["error"])
            else:
                self.analysis_completed.emit(result)

        except Exception as e:
            self.error_occurred.emit(f"分析过程中出现错误: {str(e)}")

    def _on_progress(self, progress_data: Dict[str, Any]):
        """处理进度更新"""
        self.progress_updated.emit(progress_data)

    def _on_complete(self, result_data: Dict[str, Any]):
        """处理完成事件"""
        self.analysis_completed.emit(result_data)

    def _on_error(self, error_message: str):
        """处理错误事件"""
        self.error_occurred.emit(error_message)

class SingleStockSingleAgentPage(QWidget):
    """单股单顾问分析页面"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self._init_ui()
        # 状态变量
        self.current_stocks = []
        self.current_agents = []
        self.current_start_date = ""
        self.current_end_date = ""

        # 分析相关变量
        self.analysis_worker = None
        self.is_analyzing = False

    def _init_ui(self):
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        # 页面说明
        page_desc = QLabel("🎯 单股单顾问深度分析：选择一个AI投资顾问对单只股票进行深入分析，支持持续对话探讨投资策略和风险。")
        page_desc.setStyleSheet(get_style("page_description"))
        page_desc.setWordWrap(True)
        layout.addWidget(page_desc)
        # 第一行：股票选择、Agent选择和日期选择
        top_layout = QHBoxLayout()
        # 股票输入组件
        stock_group = QWidget()
        stock_layout = QVBoxLayout(stock_group)
        stock_title = QLabel("股票选择")
        stock_title.setStyleSheet(get_style("title"))
        stock_layout.addWidget(stock_title)
        self.stock_input = StockInputView()
        self.stock_input.stocks_changed.connect(self._on_stocks_changed)
        stock_layout.addWidget(self.stock_input)
        top_layout.addWidget(stock_group)
        # Agent选择组件（单选模式）
        agent_group = QWidget()
        agent_layout = QVBoxLayout(agent_group)
        # agent_tips = QLabel("💡 选择一个AI投资顾问进行深度分析，支持持续对话探讨")
        # agent_tips.setStyleSheet(get_style("tips"))
        # agent_tips.setWordWrap(True)
        # agent_layout.addWidget(agent_tips)
        self.agent_selector = AgentSelector(multi_select=False)
        # 立即连接信号，确保能接收到初始化时的信号
        self.agent_selector.agents_changed.connect(self._on_agents_changed)

        # 手动触发一次信号以确保状态同步
        QTimer.singleShot(100, self._sync_agent_state)
        agent_layout.addWidget(self.agent_selector)
        top_layout.addWidget(agent_group)
        # 日期范围组件
        date_group = QWidget()
        date_layout = QVBoxLayout(date_group)
        date_title = QLabel("时间范围")
        date_title.setStyleSheet(get_style("title"))
        date_layout.addWidget(date_title)
        self.date_range = DateRangeView()
        self.date_range.date_range_changed.connect(self._on_date_range_changed)
        date_layout.addWidget(self.date_range)
        top_layout.addWidget(date_group)
        layout.addLayout(top_layout)
        # 第二行：开始分析按钮
        analyze_layout = QHBoxLayout()
        analyze_layout.addStretch()
        self.start_analyze_btn = QPushButton("开始分析")
        self.start_analyze_btn.setStyleSheet(get_style("start_analyze_button"))
        self.start_analyze_btn.setEnabled(False)
        self.start_analyze_btn.clicked.connect(self._start_analysis)
        analyze_layout.addWidget(self.start_analyze_btn)
        analyze_layout.addStretch()
        layout.addLayout(analyze_layout)
        # 第三行：分析结果显示
        self.results_view = ResultsView()
        self.results_view.chat_message_sent.connect(self._handle_chat_message)
        layout.addWidget(self.results_view)

        # 初始化时检查状态（延迟执行以确保所有组件都已初始化）
        QTimer.singleShot(500, self._initial_state_check)  # 增加延迟时间

    def _on_stocks_changed(self, stocks: List[str]):
        """股票选择变化处理"""
        self.current_stocks = stocks
        self._update_analyze_button_state()

    def _on_agents_changed(self, agents: List[str]):
        """AI顾问选择变化处理"""
        self.current_agents = agents
        self._update_analyze_button_state()

    def _on_date_range_changed(self, start_date: str, end_date: str):
        """日期范围变化处理"""
        self.current_start_date = start_date
        self.current_end_date = end_date
        self._update_analyze_button_state()

    def _initial_state_check(self):
        """初始状态检查"""
        # 检查股票是否已有默认选择
        if hasattr(self.stock_input, 'get_stocks'):
            selected_stocks = self.stock_input.get_stocks()
            if selected_stocks:
                self.current_stocks = selected_stocks

        # 检查AI顾问是否已有默认选择
        if hasattr(self.agent_selector, 'get_selected_agents'):
            selected_agents = self.agent_selector.get_selected_agents()
            if selected_agents:
                self.current_agents = selected_agents

        # 检查日期范围是否已设置
        if hasattr(self.date_range, 'get_date_range'):
            start_date, end_date = self.date_range.get_date_range()
            if start_date and end_date:
                self.current_start_date = start_date
                self.current_end_date = end_date

        # 更新按钮状态
        self._update_analyze_button_state()

    def _sync_agent_state(self):
        """手动同步AI顾问状态"""
        if hasattr(self.agent_selector, 'get_selected_agents'):
            selected_agents = self.agent_selector.get_selected_agents()
            if selected_agents:
                # 手动触发信号处理
                self._on_agents_changed(selected_agents)

    def _update_analyze_button_state(self):
        """更新开始分析按钮状态"""
        # 检查是否所有必要条件都满足
        has_stock = len(self.current_stocks) > 0
        has_agent = len(self.current_agents) > 0
        has_date_range = bool(self.current_start_date and self.current_end_date)

        # 只有当所有条件都满足时才启用按钮（且不在分析中）
        button_should_be_enabled = has_stock and has_agent and has_date_range and not self.is_analyzing
        self.start_analyze_btn.setEnabled(button_should_be_enabled)

    def set_market_list(self, market_list: dict):
        """设置市场列表"""
        if hasattr(self.stock_input, 'set_market_list'):
            self.stock_input.set_market_list(market_list)

    def set_stock_list(self, market_code: str, stock_list: dict):
        """设置股票列表"""
        if hasattr(self.stock_input, 'set_stock_list'):
            self.stock_input.set_stock_list(market_code, stock_list)

    def _start_analysis(self):
        """开始分析"""
        if self.is_analyzing:
            return

        # 调试信息
        print(f"开始分析 - 当前状态:")
        print(f"  股票: {self.current_stocks}")
        print(f"  AI顾问: {self.current_agents}")
        print(f"  日期范围: {self.current_start_date} 到 {self.current_end_date}")

        # 验证参数
        if not self.current_stocks or not self.current_agents:
            error_msg = f"请确保已选择股票和AI顾问\n当前状态:\n股票: {self.current_stocks}\nAI顾问: {self.current_agents}"
            QMessageBox.warning(self, "参数错误", error_msg)
            return

        if not self.current_start_date or not self.current_end_date:
            QMessageBox.warning(self, "参数错误", "请选择分析时间范围")
            return

        # 设置分析状态
        self.is_analyzing = True
        self._update_analyze_button_state()

        # 更新按钮文本和样式
        self.start_analyze_btn.setText("分析中...")

        # 设置选中的AI顾问（显示名称）
        if self.current_agents:
            agent_key = self.current_agents[0]
            agent_display_name = self._get_agent_display_name(agent_key)
            self.results_view.set_selected_agent(agent_display_name)

        # 显示加载状态
        self.results_view.show_loading_state()

        # 创建并启动分析工作线程
        self.analysis_worker = AnalysisWorker(
            tickers=self.current_stocks,
            agents=self.current_agents,
            start_date=self.current_start_date,
            end_date=self.current_end_date
        )

        # 连接信号
        self.analysis_worker.progress_updated.connect(self._on_analysis_progress)
        self.analysis_worker.analysis_completed.connect(self._on_analysis_completed)
        self.analysis_worker.error_occurred.connect(self._on_analysis_error)

        # 启动线程
        self.analysis_worker.start()

    def _on_analysis_progress(self, progress_data: Dict[str, Any]):
        """处理分析进度更新"""
        # TODO: 可以在这里显示进度信息
        pass

    def _on_analysis_completed(self, result_data: Dict[str, Any]):
        """处理分析完成"""
        self.is_analyzing = False
        self._update_analyze_button_state()

        # 恢复按钮文本
        self.start_analyze_btn.setText("开始分析")

        # 更新结果显示
        self.results_view.update_analysis_results(result_data)

        # 显示完成消息
        QMessageBox.information(self, "分析完成", "股票分析已完成，您可以查看结果并与AI顾问进行深度对话。")

    def _on_analysis_error(self, error_message: str):
        """处理分析错误"""
        self.is_analyzing = False
        self._update_analyze_button_state()

        # 恢复按钮文本
        self.start_analyze_btn.setText("开始分析")

        # 显示错误消息
        QMessageBox.critical(self, "分析错误", f"分析过程中出现错误：\n{error_message}")

    def _handle_chat_message(self, message: str):
        """处理聊天消息"""
        if not self.current_agents:
            QMessageBox.warning(self, "对话错误", "请先选择AI顾问")
            return

        # TODO: 实现与AI顾问的对话功能
        # 这里可以调用聊天API或使用现有的分析结果进行对话

        # 临时回复（实际应该调用API）
        agent_key = self.current_agents[0]
        agent_display_name = self._get_agent_display_name(agent_key)
        response = f"感谢您的问题：'{message}'。作为{agent_display_name}，我建议您关注以下几个方面..."

        # 添加AI回复到聊天历史
        self.results_view.add_agent_response(response)

    def _get_agent_display_name(self, agent_key: str) -> str:
        """根据agent key获取显示名称"""
        # 代理key到显示名称的映射
        agent_mapping = {
            "aswath_damodaran": "Aswath Damodaran",
            "ben_graham": "Ben Graham",
            "bill_ackman": "Bill Ackman",
            "cathie_wood": "Cathie Wood",
            "charlie_munger": "Charlie Munger",
            "michael_burry": "Michael Burry",
            "peter_lynch": "Peter Lynch",
            "phil_fisher": "Phil Fisher",
            "rakesh_jhunjhunwala": "Rakesh Jhunjhunwala",
            "stanley_druckenmiller": "Stanley Druckenmiller",
            "warren_buffett": "Warren Buffett"
        }
        return agent_mapping.get(agent_key, agent_key)

    def refresh_styles(self):
        """刷新样式"""
        # 刷新所有子组件的样式
        for child in self.findChildren(QLabel):
            if "title" in child.objectName():
                child.setStyleSheet(get_style("title"))
            elif "tips" in child.objectName():
                child.setStyleSheet(get_style("tips"))
            elif "page_description" in child.objectName():
                child.setStyleSheet(get_style("page_description"))
            else:
                child.setStyleSheet(get_style("label"))

        # 刷新按钮样式
        self.start_analyze_btn.setStyleSheet(get_style("start_analyze_button"))

        # 刷新子组件样式
        if hasattr(self.stock_input, 'refresh_styles'):
            self.stock_input.refresh_styles()
        if hasattr(self.agent_selector, 'refresh_styles'):
            self.agent_selector.refresh_styles()
        if hasattr(self.date_range, 'refresh_styles'):
            self.date_range.refresh_styles()
        if hasattr(self.results_view, 'refresh_styles'):
            self.results_view.refresh_styles()