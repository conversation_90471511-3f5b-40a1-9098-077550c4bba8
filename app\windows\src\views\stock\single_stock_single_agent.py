import sys
import os
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QLineEdit, QTextEdit
)
from PyQt6.QtCore import QTimer
from typing import List

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from .stock_input import StockInputView
from .date_range import DateRangeView
from ..common.results_view import ResultsView
from ..common.agent_selector import AgentSelector
from styles import get_style

class SingleStockSingleAgentPage(QWidget):
    """单股单顾问分析页面"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self._init_ui()
        # 状态变量
        self.current_stocks = []
        self.current_agents = []
        self.current_start_date = ""
        self.current_end_date = ""

    def _init_ui(self):
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        # 页面说明
        page_desc = QLabel("🎯 单股单顾问深度分析：选择一个AI投资顾问对单只股票进行深入分析，支持持续对话探讨投资策略和风险。")
        page_desc.setStyleSheet(get_style("page_description"))
        page_desc.setWordWrap(True)
        layout.addWidget(page_desc)
        # 第一行：股票选择、Agent选择和日期选择
        top_layout = QHBoxLayout()
        # 股票输入组件
        stock_group = QWidget()
        stock_layout = QVBoxLayout(stock_group)
        stock_title = QLabel("股票选择")
        stock_title.setStyleSheet(get_style("title"))
        stock_layout.addWidget(stock_title)
        self.stock_input = StockInputView()
        self.stock_input.stocks_changed.connect(self._on_stocks_changed)
        stock_layout.addWidget(self.stock_input)
        top_layout.addWidget(stock_group)
        # Agent选择组件（单选模式）
        agent_group = QWidget()
        agent_layout = QVBoxLayout(agent_group)
        # agent_tips = QLabel("💡 选择一个AI投资顾问进行深度分析，支持持续对话探讨")
        # agent_tips.setStyleSheet(get_style("tips"))
        # agent_tips.setWordWrap(True)
        # agent_layout.addWidget(agent_tips)
        self.agent_selector = AgentSelector(multi_select=False)
        self.agent_selector.agents_changed.connect(self._on_agents_changed)
        agent_layout.addWidget(self.agent_selector)
        top_layout.addWidget(agent_group)
        # 日期范围组件
        date_group = QWidget()
        date_layout = QVBoxLayout(date_group)
        date_title = QLabel("时间范围")
        date_title.setStyleSheet(get_style("title"))
        date_layout.addWidget(date_title)
        self.date_range = DateRangeView()
        self.date_range.date_range_changed.connect(self._on_date_range_changed)
        date_layout.addWidget(self.date_range)
        top_layout.addWidget(date_group)
        layout.addLayout(top_layout)
        # 第二行：开始分析按钮
        analyze_layout = QHBoxLayout()
        analyze_layout.addStretch()
        self.start_analyze_btn = QPushButton("开始分析")
        self.start_analyze_btn.setStyleSheet(get_style("start_analyze_button"))
        self.start_analyze_btn.setEnabled(False)
        analyze_layout.addWidget(self.start_analyze_btn)
        analyze_layout.addStretch()
        layout.addLayout(analyze_layout)
        # 第三行：结果显示和对话区域
        content_layout = QHBoxLayout()
        # 左侧：分析结果
        results_group = QWidget()
        results_layout = QVBoxLayout(results_group)
        results_title = QLabel("分析结果")
        results_title.setStyleSheet(get_style("title"))
        results_layout.addWidget(results_title)
        self.results_view = ResultsView()
        results_layout.addWidget(self.results_view)
        content_layout.addWidget(results_group)
        # 右侧：持续对话区域
        chat_group = QWidget()
        chat_layout = QVBoxLayout(chat_group)
        chat_title = QLabel("深度对话")
        chat_title.setStyleSheet(get_style("title"))
        chat_layout.addWidget(chat_title)
        chat_tips = QLabel("💬 与AI顾问进行深度对话，探讨投资策略、风险评估和具体操作建议")
        chat_tips.setStyleSheet(get_style("tips"))
        chat_tips.setWordWrap(True)
        chat_layout.addWidget(chat_tips)
        self.chat_history = QTextEdit()
        self.chat_history.setReadOnly(True)
        self.chat_history.setMinimumHeight(300)
        self.chat_history.setStyleSheet(get_style("text_edit"))
        chat_layout.addWidget(self.chat_history)
        chat_input_layout = QHBoxLayout()
        self.chat_input = QLineEdit()
        self.chat_input.setPlaceholderText("输入您的问题，与AI顾问深入探讨...")
        self.chat_input.setStyleSheet(get_style("input"))
        chat_input_layout.addWidget(self.chat_input)
        self.chat_send_btn = QPushButton("发送")
        self.chat_send_btn.setStyleSheet(get_style("button_primary"))
        chat_input_layout.addWidget(self.chat_send_btn)
        chat_layout.addLayout(chat_input_layout)
        content_layout.addWidget(chat_group)
        content_layout.setStretch(0, 2)
        content_layout.setStretch(1, 1)
        layout.addLayout(content_layout)

        # 初始化时检查状态（延迟执行以确保所有组件都已初始化）
        QTimer.singleShot(100, self._initial_state_check)

    def _on_stocks_changed(self, stocks: List[str]):
        """股票选择变化处理"""
        self.current_stocks = stocks
        self._update_analyze_button_state()

    def _on_agents_changed(self, agents: List[str]):
        """AI顾问选择变化处理"""
        self.current_agents = agents
        self._update_analyze_button_state()

    def _on_date_range_changed(self, start_date: str, end_date: str):
        """日期范围变化处理"""
        self.current_start_date = start_date
        self.current_end_date = end_date
        self._update_analyze_button_state()

    def _initial_state_check(self):
        """初始状态检查"""
        # 检查AI顾问是否已有默认选择
        if hasattr(self.agent_selector, 'get_selected_agents'):
            selected_agents = self.agent_selector.get_selected_agents()
            if selected_agents:
                self.current_agents = selected_agents
                print(f"初始化时发现已选择的AI顾问: {selected_agents}")

        # 检查日期范围是否已设置
        if hasattr(self.date_range, 'get_date_range'):
            start_date, end_date = self.date_range.get_date_range()
            if start_date and end_date:
                self.current_start_date = start_date
                self.current_end_date = end_date
                print(f"初始化时发现已设置的日期范围: {start_date} 到 {end_date}")

        # 更新按钮状态
        self._update_analyze_button_state()

    def _update_analyze_button_state(self):
        """更新开始分析按钮状态"""
        # 检查是否所有必要条件都满足
        has_stock = len(self.current_stocks) > 0
        has_agent = len(self.current_agents) > 0
        has_date_range = bool(self.current_start_date and self.current_end_date)

        # 调试信息
        print(f"按钮状态检查:")
        print(f"  股票: {self.current_stocks} (有效: {has_stock})")
        print(f"  AI顾问: {self.current_agents} (有效: {has_agent})")
        print(f"  日期范围: {self.current_start_date} 到 {self.current_end_date} (有效: {has_date_range})")

        # 只有当所有条件都满足时才启用按钮
        button_should_be_enabled = has_stock and has_agent and has_date_range
        self.start_analyze_btn.setEnabled(button_should_be_enabled)
        print(f"  按钮状态: {'启用' if button_should_be_enabled else '禁用'}")
        print()

    def set_market_list(self, market_list: dict):
        """设置市场列表"""
        if hasattr(self.stock_input, 'set_market_list'):
            self.stock_input.set_market_list(market_list)

    def set_stock_list(self, market_code: str, stock_list: dict):
        """设置股票列表"""
        if hasattr(self.stock_input, 'set_stock_list'):
            self.stock_input.set_stock_list(market_code, stock_list)

    def refresh_styles(self):
        """刷新样式"""
        # 刷新所有子组件的样式
        for child in self.findChildren(QLabel):
            if "title" in child.objectName():
                child.setStyleSheet(get_style("title"))
            elif "tips" in child.objectName():
                child.setStyleSheet(get_style("tips"))
            elif "page_description" in child.objectName():
                child.setStyleSheet(get_style("page_description"))
            else:
                child.setStyleSheet(get_style("label"))

        # 刷新按钮样式
        self.start_analyze_btn.setStyleSheet(get_style("start_analyze_button"))
        self.chat_send_btn.setStyleSheet(get_style("button_primary"))

        # 刷新输入框样式
        self.chat_input.setStyleSheet(get_style("input"))
        self.chat_history.setStyleSheet(get_style("text_edit"))

        # 刷新子组件样式
        if hasattr(self.stock_input, 'refresh_styles'):
            self.stock_input.refresh_styles()
        if hasattr(self.agent_selector, 'refresh_styles'):
            self.agent_selector.refresh_styles()
        if hasattr(self.date_range, 'refresh_styles'):
            self.date_range.refresh_styles()
        if hasattr(self.results_view, 'refresh_styles'):
            self.results_view.refresh_styles()