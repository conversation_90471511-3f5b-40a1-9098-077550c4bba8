#!/usr/bin/env python3
"""
测试单股单顾问界面改进
"""

import sys
import os

# 添加项目路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.insert(0, project_root)

# 添加src目录到路径
src_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "src")
if src_dir not in sys.path:
    sys.path.insert(0, src_dir)

try:
    from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QLabel
    from PyQt6.QtCore import QTimer
    from views.stock.single_stock_single_agent import SingleStockSingleAgentPage
    
    class TestWindow(QMainWindow):
        """测试窗口"""
        
        def __init__(self):
            super().__init__()
            self.setWindowTitle("单股单顾问界面改进测试")
            self.setGeometry(100, 100, 1200, 800)
            
            # 创建中央部件
            central_widget = QWidget()
            self.setCentralWidget(central_widget)
            
            # 创建布局
            layout = QVBoxLayout(central_widget)
            
            # 添加信息标签
            info_label = QLabel("测试单股单顾问界面的改进功能")
            info_label.setStyleSheet("font-size: 14px; font-weight: bold; padding: 10px;")
            layout.addWidget(info_label)
            
            # 创建单股单顾问页面
            self.single_stock_page = SingleStockSingleAgentPage()
            layout.addWidget(self.single_stock_page)
            
            # 添加测试按钮
            button_layout = QVBoxLayout()
            
            self.test_market_btn = QPushButton("测试设置市场列表")
            self.test_market_btn.clicked.connect(self.test_set_market_list)
            button_layout.addWidget(self.test_market_btn)
            
            self.test_stock_btn = QPushButton("测试设置股票列表")
            self.test_stock_btn.clicked.connect(self.test_set_stock_list)
            button_layout.addWidget(self.test_stock_btn)
            
            self.check_selection_btn = QPushButton("检查当前选择")
            self.check_selection_btn.clicked.connect(self.check_current_selection)
            button_layout.addWidget(self.check_selection_btn)
            
            layout.addLayout(button_layout)
            
        def test_set_market_list(self):
            """测试设置市场列表"""
            print("=== 测试设置市场列表 ===")
            market_list = {
                "SH": "上海证券交易所",
                "SZ": "深圳证券交易所",
                "BJ": "北京证券交易所"
            }
            self.single_stock_page.set_market_list(market_list)
            print(f"已设置市场列表: {market_list}")
            print("检查第一个市场是否自动选中")
            
        def test_set_stock_list(self):
            """测试设置股票列表"""
            print("\n=== 测试设置股票列表 ===")
            stock_list = {
                "000001": "平安银行",
                "000002": "万科A",
                "000858": "五粮液",
                "600000": "浦发银行",
                "600036": "招商银行",
                "600519": "贵州茅台"
            }
            # 为上海市场设置股票列表
            self.single_stock_page.set_stock_list("SH", stock_list)
            print(f"已为上海市场设置股票列表: {stock_list}")
            print("检查第一个股票是否自动选中")
            
        def check_current_selection(self):
            """检查当前选择状态"""
            print("\n=== 当前选择状态 ===")
            
            # 检查股票选择
            stocks = self.single_stock_page.current_stocks
            print(f"当前选中的股票: {stocks}")
            
            # 检查AI顾问选择
            agents = self.single_stock_page.current_agents
            print(f"当前选中的AI顾问: {agents}")
            
            # 检查日期范围
            start_date = self.single_stock_page.current_start_date
            end_date = self.single_stock_page.current_end_date
            print(f"当前日期范围: {start_date} 到 {end_date}")
            
            # 检查分析按钮状态
            button_enabled = self.single_stock_page.start_analyze_btn.isEnabled()
            print(f"开始分析按钮状态: {'启用' if button_enabled else '禁用'}")
            
            # 检查AI顾问选择器的详细状态
            selected_agents = self.single_stock_page.agent_selector.get_selected_agents()
            print(f"AI顾问选择器返回的选中顾问: {selected_agents}")
            
            print()
    
    def test_single_stock_improvements():
        """测试单股单顾问界面改进"""
        print("=== 单股单顾问界面改进测试 ===")
        
        app = QApplication(sys.argv)
        
        # 创建测试窗口
        window = TestWindow()
        window.show()
        
        print("单股单顾问界面已启动")
        print("改进功能测试:")
        print("1. 股票市场选择 - 检查第一个市场是否自动选中")
        print("2. 股票选择 - 检查第一个股票是否自动选中")
        print("3. AI投资顾问选择 - 检查是否使用下拉框单选模式")
        print("4. 检查第一个AI顾问是否自动选中并显示详细信息")
        print("5. 测试选择状态管理和按钮启用逻辑")
        print("6. 点击测试按钮验证功能")
        print("7. 验证没有CSS错误（Unknown property content）")
        print("\n请在界面中进行交互测试...")
        print("注意观察:")
        print("- 市场下拉框是否自动选择第一个选项")
        print("- 股票下拉框是否自动选择第一个股票")
        print("- AI顾问选择是否显示为下拉框而非单选按钮")
        print("- 第一个AI顾问是否默认选中")
        print("- AI顾问详细信息是否正确显示")
        print("- 所有默认选择完成后，开始分析按钮是否自动启用")
        print("- 控制台是否有调试信息输出")
        
        # 运行应用
        sys.exit(app.exec())
    
    if __name__ == "__main__":
        test_single_stock_improvements()
        
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保PyQt6已安装: pip install PyQt6")
    print("或者检查项目路径设置是否正确")
except Exception as e:
    print(f"测试过程中出现错误: {e}")
    import traceback
    traceback.print_exc()
