"""
基础API客户端
提供HTTP请求的基础功能，支持调试日志和日志管理
"""

import requests
import json
import logging
import os
import time
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, Union
from urllib.parse import urljoin
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
from logging.handlers import RotatingFileHandler
import threading


class APILogger:
    """API调试日志管理器"""

    _instance = None
    _lock = threading.Lock()

    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        if hasattr(self, '_initialized'):
            return

        self._initialized = True
        self.debug_enabled = True  # 默认启用调试
        self.console_enabled = True  # 控制台输出
        self.file_enabled = True  # 文件输出

        # 日志配置 - 尝试使用ConfigManager获取统一目录
        try:
            from utils.config_manager import get_config_manager
            config_manager = get_config_manager()
            self.log_dir = config_manager.get_log_directory()
        except ImportError:
            # 后备方案：使用固定目录
            self.log_dir = os.path.join(os.path.expanduser("~"), "topai", "logs")
            os.makedirs(self.log_dir, exist_ok=True)

        self.max_file_size = 10 * 1024 * 1024  # 10MB
        self.max_files = 5  # 保留5个日志文件
        self.cleanup_days = 7  # 清理7天前的日志

        # 设置日志器
        self.logger = logging.getLogger('api_client')
        self.logger.setLevel(logging.DEBUG)

        # 清除现有处理器
        self.logger.handlers.clear()

        # 设置日志格式
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )

        # 控制台处理器
        if self.console_enabled:
            console_handler = logging.StreamHandler()
            console_handler.setLevel(logging.INFO)
            console_handler.setFormatter(formatter)
            self.logger.addHandler(console_handler)

        # 文件处理器（轮转）
        if self.file_enabled:
            log_file = os.path.join(self.log_dir, "api_client.log")
            file_handler = RotatingFileHandler(
                log_file,
                maxBytes=self.max_file_size,
                backupCount=self.max_files,
                encoding='utf-8'
            )
            file_handler.setLevel(logging.DEBUG)
            file_handler.setFormatter(formatter)
            self.logger.addHandler(file_handler)

        # 启动日志清理线程
        self._start_cleanup_thread()

    def _start_cleanup_thread(self):
        """启动日志清理线程"""
        def cleanup_old_logs():
            while True:
                try:
                    self._cleanup_old_logs()
                    time.sleep(24 * 60 * 60)  # 每24小时清理一次
                except Exception as e:
                    self.logger.error(f"日志清理失败: {e}")
                    time.sleep(60 * 60)  # 出错后1小时重试

        cleanup_thread = threading.Thread(target=cleanup_old_logs, daemon=True)
        cleanup_thread.start()

    def _cleanup_old_logs(self):
        """清理过期日志文件"""
        if not os.path.exists(self.log_dir):
            return

        cutoff_time = datetime.now() - timedelta(days=self.cleanup_days)

        for filename in os.listdir(self.log_dir):
            if filename.startswith("api_client.log"):
                file_path = os.path.join(self.log_dir, filename)
                try:
                    file_time = datetime.fromtimestamp(os.path.getmtime(file_path))
                    if file_time < cutoff_time:
                        os.remove(file_path)
                        self.logger.info(f"清理过期日志文件: {filename}")
                except Exception as e:
                    self.logger.error(f"清理日志文件失败 {filename}: {e}")

    def enable_debug(self, enabled: bool = True):
        """启用/禁用调试模式"""
        self.debug_enabled = enabled
        if enabled:
            self.logger.info("API调试模式已启用")
        else:
            self.logger.info("API调试模式已禁用")

    def enable_console(self, enabled: bool = True):
        """启用/禁用控制台输出"""
        self.console_enabled = enabled
        # 重新配置处理器
        self._reconfigure_handlers()

    def enable_file_logging(self, enabled: bool = True):
        """启用/禁用文件日志"""
        self.file_enabled = enabled
        # 重新配置处理器
        self._reconfigure_handlers()

    def _reconfigure_handlers(self):
        """重新配置日志处理器"""
        # 清除现有处理器
        self.logger.handlers.clear()

        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )

        # 控制台处理器
        if self.console_enabled:
            console_handler = logging.StreamHandler()
            console_handler.setLevel(logging.INFO)
            console_handler.setFormatter(formatter)
            self.logger.addHandler(console_handler)

        # 文件处理器
        if self.file_enabled:
            log_file = os.path.join(self.log_dir, "api_client.log")
            file_handler = RotatingFileHandler(
                log_file,
                maxBytes=self.max_file_size,
                backupCount=self.max_files,
                encoding='utf-8'
            )
            file_handler.setLevel(logging.DEBUG)
            file_handler.setFormatter(formatter)
            self.logger.addHandler(file_handler)

    def log_request(self, method: str, url: str, params: Optional[Dict] = None,
                   data: Optional[Dict] = None, headers: Optional[Dict] = None):
        """记录请求信息"""
        if not self.debug_enabled:
            return

        self.logger.info(f"🚀 API请求: {method} {url}")

        if params:
            self.logger.debug(f"📋 请求参数: {self._safe_serialize(params)}")

        if data:
            # 敏感数据脱敏
            safe_data = self._sanitize_data(data)
            self.logger.debug(f"📦 请求体: {self._safe_serialize(safe_data)}")

        if headers:
            # 敏感头部脱敏
            safe_headers = self._sanitize_headers(headers)
            self.logger.debug(f"📋 请求头: {self._safe_serialize(safe_headers)}")

    def log_response(self, method: str, url: str, status_code: int,
                    response_data: Optional[Dict] = None, duration: float = 0):
        """记录响应信息"""
        if not self.debug_enabled:
            return

        status_emoji = "✅" if 200 <= status_code < 300 else "❌"
        self.logger.info(f"{status_emoji} API响应: {method} {url} - {status_code} ({duration:.2f}s)")

        if response_data:
            # 限制响应数据大小
            safe_data = self._truncate_data(response_data)
            self.logger.debug(f"📥 响应数据: {self._safe_serialize(safe_data)}")

    def log_error(self, method: str, url: str, error: Exception, duration: float = 0):
        """记录错误信息"""
        if not self.debug_enabled:
            return

        self.logger.error(f"💥 API错误: {method} {url} - {type(error).__name__}: {str(error)} ({duration:.2f}s)")

    def _sanitize_data(self, data: Dict) -> Dict:
        """脱敏敏感数据"""
        if not isinstance(data, dict):
            return data

        sensitive_keys = ['password', 'token', 'key', 'secret', 'auth', 'credential']
        sanitized = {}

        for key, value in data.items():
            if any(sensitive in key.lower() for sensitive in sensitive_keys):
                sanitized[key] = "***HIDDEN***"
            elif isinstance(value, dict):
                sanitized[key] = self._sanitize_data(value)
            else:
                sanitized[key] = value

        return sanitized

    def _sanitize_headers(self, headers: Dict) -> Dict:
        """脱敏敏感请求头"""
        if not isinstance(headers, dict):
            return headers

        sensitive_headers = ['authorization', 'x-api-key', 'cookie', 'set-cookie']
        sanitized = {}

        for key, value in headers.items():
            if key.lower() in sensitive_headers:
                sanitized[key] = "***HIDDEN***"
            else:
                sanitized[key] = value

        return sanitized

    def _truncate_data(self, data: Any, max_length: int = 1000) -> Any:
        """截断过长的数据"""
        if isinstance(data, str) and len(data) > max_length:
            return data[:max_length] + "...[截断]"
        elif isinstance(data, dict):
            truncated = {}
            for key, value in data.items():
                truncated[key] = self._truncate_data(value, max_length)
            return truncated
        elif isinstance(data, list) and len(data) > 10:
            return data[:10] + ["...[截断]"]
        else:
            return data

    def _safe_serialize(self, data: Any) -> str:
        """安全序列化数据，处理不可JSON序列化的对象"""
        try:
            return json.dumps(data, ensure_ascii=False, indent=2)
        except (TypeError, ValueError) as e:
            # 如果JSON序列化失败，尝试转换为可序列化的格式
            try:
                serializable_data = self._make_serializable(data)
                return json.dumps(serializable_data, ensure_ascii=False, indent=2)
            except:
                # 最后的备选方案：转换为字符串
                return str(data)

    def _make_serializable(self, obj: Any) -> Any:
        """将对象转换为可JSON序列化的格式"""
        if obj is None or isinstance(obj, (bool, int, float, str)):
            return obj
        elif isinstance(obj, dict):
            # 处理字典类型（包括CaseInsensitiveDict等）
            return {str(k): self._make_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, (list, tuple)):
            return [self._make_serializable(item) for item in obj]
        elif hasattr(obj, '__dict__'):
            # 处理有__dict__属性的对象
            return {str(k): self._make_serializable(v) for k, v in obj.__dict__.items()}
        elif hasattr(obj, '_asdict'):
            # 处理namedtuple
            return self._make_serializable(obj._asdict())
        else:
            # 其他情况转换为字符串
            return str(obj)


class APIClient:
    """基础API客户端类，支持调试日志"""

    def __init__(self, base_url: str = "http://localhost:8000", max_retries: int = 3,
                 debug: bool = True):
        """
        初始化API客户端

        Args:
            base_url: API服务器的基础URL
            max_retries: 最大重试次数，默认3次
            debug: 是否启用调试模式，默认True
        """
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()

        # 初始化日志器
        self.logger = APILogger()
        if not debug:
            self.logger.enable_debug(False)

        # 设置全局重试机制
        retry_strategy = Retry(
            total=max_retries,
            backoff_factor=1,
            status_forcelist=[500, 502, 503, 504],
            allowed_methods=["HEAD", "GET", "OPTIONS", "POST", "PUT", "DELETE"]
        )
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount('http://', adapter)
        self.session.mount('https://', adapter)

        # 设置默认请求头
        self.session.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        })

        self.logger.logger.info(f"API客户端初始化完成: {self.base_url}")

    def enable_debug(self, enabled: bool = True):
        """启用/禁用调试模式"""
        self.logger.enable_debug(enabled)

    def enable_console_logging(self, enabled: bool = True):
        """启用/禁用控制台日志"""
        self.logger.enable_console(enabled)

    def enable_file_logging(self, enabled: bool = True):
        """启用/禁用文件日志"""
        self.logger.enable_file_logging(enabled)
    
    def _make_request(
        self,
        method: str,
        endpoint: str,
        params: Optional[Dict[str, Any]] = None,
        data: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
        timeout: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        发送HTTP请求

        Args:
            method: HTTP方法 (GET, POST, PUT, DELETE)
            endpoint: API端点路径
            params: URL参数
            data: 请求体数据
            headers: 请求头
            timeout: 超时时间（秒），默认30秒

        Returns:
            Dict[str, Any]: 响应数据

        Raises:
            requests.RequestException: 请求失败时抛出
        """
        url = urljoin(self.base_url, endpoint)

        # 合并请求头
        request_headers = self.session.headers.copy()
        if headers:
            request_headers.update(headers)

        # 设置超时时间
        request_timeout = timeout if timeout is not None else 30

        # 记录请求开始时间
        start_time = time.time()

        # 记录请求信息
        self.logger.log_request(method, url, params, data, request_headers)

        try:
            response = self.session.request(
                method=method,
                url=url,
                params=params,
                json=data,
                headers=request_headers,
                timeout=request_timeout
            )

            # 计算请求耗时
            duration = time.time() - start_time

            # 检查响应状态码
            response.raise_for_status()

            # 解析响应数据
            try:
                response_data = response.json()
            except json.JSONDecodeError:
                response_data = {"raw_response": response.text}

            # 记录响应信息
            self.logger.log_response(method, url, response.status_code, response_data, duration)

            return response_data

        except requests.exceptions.RequestException as e:
            # 计算请求耗时
            duration = time.time() - start_time

            # 记录错误信息
            self.logger.log_error(method, url, e, duration)

            # 重新抛出异常
            raise
    
    def get(self, endpoint: str, params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """发送GET请求"""
        return self._make_request('GET', endpoint, params=params)
    
    def post(self, endpoint: str, data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """发送POST请求"""
        return self._make_request('POST', endpoint, data=data)
    
    def put(self, endpoint: str, data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """发送PUT请求"""
        return self._make_request('PUT', endpoint, data=data)
    
    def delete(self, endpoint: str) -> Dict[str, Any]:
        """发送DELETE请求"""
        return self._make_request('DELETE', endpoint)
    
    def health_check(self) -> bool:
        """
        检查API服务器健康状态

        Returns:
            bool: 服务器是否健康
        """
        url = self.base_url + '/ping'
        start_time = time.time()

        try:
            self.logger.log_request('GET', url)
            response = self.session.get(url, timeout=3)
            duration = time.time() - start_time

            if response.status_code == 200:
                self.logger.log_response('GET', url, response.status_code,
                                       {"status": "healthy"}, duration)
                return True
            else:
                self.logger.log_response('GET', url, response.status_code,
                                       {"status": "unhealthy"}, duration)
                return False
        except Exception as e:
            duration = time.time() - start_time
            self.logger.log_error('GET', url, e, duration)
            return False
    
    def close(self):
        """关闭客户端连接"""
        self.session.close()
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()
        return False  # 不抑制异常