"""
基础API客户端
提供HTTP请求的基础功能
"""

import requests
import json
from typing import Dict, Any, Optional
from urllib.parse import urljoin
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry


class APIClient:
    """基础API客户端类"""
    
    def __init__(self, base_url: str = "http://localhost:8000", max_retries: int = 3):
        """
        初始化API客户端
        
        Args:
            base_url: API服务器的基础URL
            max_retries: 最大重试次数，默认3次
        """
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        
        # 设置全局重试机制
        retry_strategy = Retry(
            total=max_retries,
            backoff_factor=1,
            status_forcelist=[500, 502, 503, 504],
            allowed_methods=["HEAD", "GET", "OPTIONS", "POST", "PUT", "DELETE"]
        )
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount('http://', adapter)
        self.session.mount('https://', adapter)
        
        # 设置默认请求头
        self.session.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        })
    
    def _make_request(
        self, 
        method: str, 
        endpoint: str, 
        params: Optional[Dict[str, Any]] = None,
        data: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
        timeout: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        发送HTTP请求
        
        Args:
            method: HTTP方法 (GET, POST, PUT, DELETE)
            endpoint: API端点路径
            params: URL参数
            data: 请求体数据
            headers: 请求头
            timeout: 超时时间（秒），默认30秒
            
        Returns:
            Dict[str, Any]: 响应数据
            
        Raises:
            requests.RequestException: 请求失败时抛出
        """
        url = urljoin(self.base_url, endpoint)
        
        # 合并请求头
        request_headers = self.session.headers.copy()
        if headers:
            request_headers.update(headers)
        
        # 设置超时时间
        request_timeout = timeout if timeout is not None else 30
        
        try:
            response = self.session.request(
                method=method,
                url=url,
                params=params,
                json=data,
                headers=request_headers,
                timeout=request_timeout
            )
            
            # 检查响应状态码
            response.raise_for_status()
            
            # 返回JSON数据
            return response.json()
            
        except requests.exceptions.RequestException as e:
            # 记录错误信息
            print(f"API请求失败: {method} {url}")
            print(f"错误详情: {str(e)}")
            raise
    
    def get(self, endpoint: str, params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """发送GET请求"""
        return self._make_request('GET', endpoint, params=params)
    
    def post(self, endpoint: str, data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """发送POST请求"""
        return self._make_request('POST', endpoint, data=data)
    
    def put(self, endpoint: str, data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """发送PUT请求"""
        return self._make_request('PUT', endpoint, data=data)
    
    def delete(self, endpoint: str) -> Dict[str, Any]:
        """发送DELETE请求"""
        return self._make_request('DELETE', endpoint)
    
    def health_check(self) -> bool:
        """
        检查API服务器健康状态
        
        Returns:
            bool: 服务器是否健康
        """
        try:
            url = self.base_url + '/ping'
            response = self.session.get(url, timeout=3)
            if response.status_code == 200:
                return True
            else:
                print(f"API健康检查失败: 状态码 {response.status_code}")
                return False
        except Exception as e:
            print(f"API健康检查失败: {str(e)}")
            return False
    
    def close(self):
        """关闭客户端连接"""
        self.session.close()
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close() 