#!/usr/bin/env python3
"""
测试LLM配置界面
"""

import sys
import os

# 添加项目路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.insert(0, project_root)

# 添加src目录到路径
src_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "src")
if src_dir not in sys.path:
    sys.path.insert(0, src_dir)

try:
    from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
    from PyQt6.QtCore import QTimer
    from views.settings.llm_config import LLMConfigView
    
    class TestWindow(QMainWindow):
        """测试窗口"""
        
        def __init__(self):
            super().__init__()
            self.setWindowTitle("LLM配置界面测试")
            self.setGeometry(100, 100, 800, 600)
            
            # 创建中央部件
            central_widget = QWidget()
            self.setCentralWidget(central_widget)
            
            # 创建布局
            layout = QVBoxLayout(central_widget)
            
            # 创建LLM配置视图
            self.llm_config = LLMConfigView(api_base_url="http://localhost:8000")
            self.llm_config.config_changed.connect(self.on_config_changed)
            layout.addWidget(self.llm_config)
            
        def on_config_changed(self, config):
            """处理配置变化"""
            print("配置已更改:")
            print(f"  选中的模型: {config.get('selected_model')}")
            print(f"  API基础URL: {config.get('api_base_url')}")
            print(f"  当前标签页: {config.get('tab_index')}")
    
    def test_llm_config_interface():
        """测试LLM配置界面"""
        print("=== LLM配置界面测试 ===")
        
        app = QApplication(sys.argv)
        
        # 创建测试窗口
        window = TestWindow()
        window.show()
        
        print("LLM配置界面已启动")
        print("功能测试:")
        print("1. 检查'可用模型'标签页是否正确显示")
        print("2. 检查模型加载状态和进度条")
        print("3. 检查'自定义配置'标签页的占位符内容")
        print("4. 测试模型选择和详情显示")
        print("5. 测试连接测试和保存配置功能")
        print("\n请在界面中进行交互测试...")
        
        # 运行应用
        sys.exit(app.exec())
    
    if __name__ == "__main__":
        test_llm_config_interface()
        
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保PyQt6已安装: pip install PyQt6")
    print("或者检查项目路径设置是否正确")
except Exception as e:
    print(f"测试过程中出现错误: {e}")
    import traceback
    traceback.print_exc()
