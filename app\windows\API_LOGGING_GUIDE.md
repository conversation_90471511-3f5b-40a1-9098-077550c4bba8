# API调试日志功能使用指南

## 概述

新增的API调试日志功能提供了完整的API请求/响应记录、一键开关控制、自动日志管理等功能，帮助开发者更好地调试和监控API调用。

## 主要特性

### 🚀 **核心功能**
- ✅ **详细的请求/响应日志记录**
- ✅ **一键启用/禁用调试模式**
- ✅ **控制台和文件双重输出**
- ✅ **敏感数据自动脱敏**
- ✅ **日志文件自动轮转**
- ✅ **过期日志自动清理**
- ✅ **流式请求特殊支持**

### 📊 **日志管理**
- ✅ **文件大小限制**（默认10MB）
- ✅ **文件数量限制**（默认5个）
- ✅ **自动清理机制**（默认7天）
- ✅ **配置持久化存储**

## 快速开始

### 1. 基础使用

```python
from api.client import APIClient
from api.hedge_fund_client import HedgeFundClient

# 创建带调试的API客户端
client = APIClient("http://localhost:8000", debug=True)

# 创建对冲基金客户端
hf_client = HedgeFundClient("http://localhost:8000", debug=True)
```

### 2. 一键控制

```python
from api.logger_config import (
    enable_api_debug,
    disable_all_api_logging,
    enable_minimal_api_logging,
    enable_full_api_logging
)

# 一键启用调试
enable_api_debug(True)

# 一键关闭所有日志
disable_all_api_logging()

# 启用最小日志（仅错误）
enable_minimal_api_logging()

# 启用完整日志
enable_full_api_logging()
```

### 3. 日志管理

```python
from api.logger_config import logger_config, get_api_log_directory, clear_api_logs

# 查看日志目录
print(get_api_log_directory())

# 查看日志文件
log_files = logger_config.get_log_files()
for log_file in log_files:
    print(f"{log_file['name']}: {log_file['size_mb']} MB")

# 清空所有日志
clear_api_logs()

# 自定义配置
logger_config.set_file_size_limit(20)  # 20MB
logger_config.set_max_files(10)        # 10个文件
logger_config.set_cleanup_days(14)     # 14天清理
```

## 详细功能说明

### 日志输出格式

#### 请求日志
```
2025-06-21 10:30:15 - api_client - INFO - 🚀 API请求: POST http://localhost:8000/hedge-fund/run
2025-06-21 10:30:15 - api_client - DEBUG - 📦 请求体: {
  "tickers": ["AAPL"],
  "selected_agents": ["warren_buffett"],
  "start_date": "2024-01-01",
  "end_date": "2024-01-31"
}
```

#### 响应日志
```
2025-06-21 10:30:18 - api_client - INFO - ✅ API响应: POST http://localhost:8000/hedge-fund/run - 200 (2.85s)
2025-06-21 10:30:18 - api_client - DEBUG - 📥 响应数据: {
  "status": "success",
  "data": {...}
}
```

#### 错误日志
```
2025-06-21 10:30:20 - api_client - ERROR - 💥 API错误: POST http://localhost:8000/hedge-fund/run - ConnectionError: Connection refused (0.05s)
```

#### 流式请求日志
```
2025-06-21 10:30:25 - api_client - DEBUG - 📡 流式事件 #1: start
2025-06-21 10:30:26 - api_client - DEBUG - 📡 流式事件 #2: progress
2025-06-21 10:30:30 - api_client - INFO - ✅ API响应: POST http://localhost:8000/hedge-fund/run - 200 (5.12s)
```

### 敏感数据脱敏

自动脱敏以下类型的敏感数据：
- 密码字段（password, secret, key等）
- 认证头部（Authorization, X-API-Key等）
- Cookie信息

```python
# 原始数据
{
    "username": "user123",
    "password": "secret123",
    "api_key": "sk-1234567890"
}

# 脱敏后
{
    "username": "user123",
    "password": "***HIDDEN***",
    "api_key": "***HIDDEN***"
}
```

### 配置选项

#### 日志级别控制
```python
# 完整调试模式（DEBUG级别）
enable_full_api_logging()

# 最小日志模式（ERROR级别）
enable_minimal_api_logging()

# 完全关闭
disable_all_api_logging()
```

#### 输出控制
```python
# 只输出到控制台
logger_config.enable_console(True)
logger_config.enable_file_logging(False)

# 只输出到文件
logger_config.enable_console(False)
logger_config.enable_file_logging(True)

# 双重输出
logger_config.enable_console(True)
logger_config.enable_file_logging(True)
```

#### 文件管理
```python
# 设置单个文件大小限制
logger_config.set_file_size_limit(20)  # 20MB

# 设置最大文件数量
logger_config.set_max_files(10)

# 设置自动清理天数
logger_config.set_cleanup_days(14)
```

## 文件结构

### 日志文件位置
```
~/.ai-hedge-fund/
├── logs/
│   ├── api_client.log          # 当前日志文件
│   ├── api_client.log.1        # 轮转日志文件1
│   ├── api_client.log.2        # 轮转日志文件2
│   └── ...
└── logger_config.json          # 配置文件
```

### 配置文件格式
```json
{
  "debug_enabled": true,
  "console_enabled": true,
  "file_enabled": true,
  "max_file_size_mb": 10,
  "max_files": 5,
  "cleanup_days": 7
}
```

## 性能考虑

### 日志开销
- **控制台输出**: 最小开销
- **文件输出**: 轻微I/O开销
- **数据脱敏**: 微小CPU开销
- **JSON格式化**: 适中开销

### 优化建议
1. **生产环境**: 使用 `enable_minimal_api_logging()` 或完全关闭
2. **开发环境**: 使用 `enable_full_api_logging()`
3. **调试时**: 临时启用完整日志，调试完成后关闭

## 常见使用场景

### 1. 开发调试
```python
# 启用完整日志进行调试
enable_full_api_logging()

# 执行API调用
client = HedgeFundClient()
result = client.run_hedge_fund_sync(...)

# 调试完成后关闭
disable_all_api_logging()
```

### 2. 生产监控
```python
# 只记录错误信息
enable_minimal_api_logging()

# 定期检查日志
log_files = logger_config.get_log_files()
if any(f['size_mb'] > 50 for f in log_files):
    # 处理大日志文件
    clear_api_logs()
```

### 3. 问题排查
```python
# 临时启用详细日志
enable_full_api_logging()

# 重现问题
# ...

# 查看日志文件
log_dir = get_api_log_directory()
print(f"检查日志: {log_dir}/api_client.log")
```

## 测试和验证

运行测试脚本验证功能：
```bash
cd app/windows/test
python test_api_logging.py
```

测试内容包括：
- 基础API日志记录
- 对冲基金客户端日志
- 日志控制功能
- 流式请求日志
- 日志管理功能

## 故障排除

### 常见问题

1. **日志文件无法创建**
   - 检查目录权限
   - 确保磁盘空间充足

2. **配置不生效**
   - 检查配置文件格式
   - 重新创建配置文件

3. **日志输出过多**
   - 使用 `enable_minimal_api_logging()`
   - 或完全关闭日志

4. **性能影响**
   - 关闭文件日志，只保留控制台输出
   - 减少日志级别

### 重置配置
```python
# 重置为默认配置
logger_config.reset_to_default()

# 或手动删除配置文件
import os
config_file = os.path.expanduser("~/.ai-hedge-fund/logger_config.json")
if os.path.exists(config_file):
    os.remove(config_file)
```

## 总结

新的API日志功能提供了：
- 🎯 **精确的调试信息**
- 🔧 **灵活的控制选项**
- 📁 **智能的文件管理**
- 🔒 **安全的数据处理**
- ⚡ **优秀的性能表现**

通过合理使用这些功能，可以大大提高API调试和问题排查的效率。
